:root {
  --gatx-typography-font-family:
    system-ui, -apple-system, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>,
    'Noto Sans', 'Liberation Sans', sans-serif;

  /* Body Font Sizes, based on 16px Root */
  --gatx-typography-font-size-xs: 0.75rem; /* 12px */
  --gatx-typography-font-size-sm: 0.875rem; /* 14px */
  --gatx-typography-font-size-regular: 1rem; /*  16px */
  --gatx-typography-font-size-sm-lead: 1.125rem; /* 18px */
  --gatx-typography-font-size-lead: 1.25rem; /* 20px */

  --gatx-typography-line-height: 1.5;
  --gatx-typography-line-height-title: 1.2;

  --gatx-typography-font-weight-light: 300;
  --gatx-typography-font-weight-regular: 400;
  --gatx-typography-font-weight-medium: 500;
  --gatx-typography-font-weight-bold: 700;

  --gatx-typography-color-text: var(--gatx-colors-dark-8);
  --gatx-typography-color-text-subtle: var(--gatx-colors-dark-5);
  --gatx-typography-color-link: var(--gatx-colors-secondary-6);
  --gatx-typography-color-link-hover: var(--gatx-colors-secondary-7);
}

@media (prefers-color-scheme: dark) {
  :root {
    --gatx-typography-color-text: var(--gatx-colors-light-1);
    --gatx-typography-color-text-subtle: var(--gatx-colors-dark-2);
    --gatx-typography-color-link: var(--gatx-colors-secondary-5);
    --gatx-typography-color-link-hover: var(--gatx-colors-secondary-6);
  }
}

html {
  font-size: 16px;
}

html,
body {
  color: var(--gatx-typography-color-text);
  font-stretch: normal;
  font-family: var(--gatx-typography-font-family);
}

body {
  font-weight: var(--gatx-typography-font-weight-regular);
  font-size: var(--gatx-typography-font-size-regular);
  line-height: var(--gatx-typography-line-height);
}

import { Badge as BootstrapBadge } from 'react-bootstrap'

export type BadgeVariant =
  | 'primary'
  | 'outline'
  | 'info'
  | 'warning'
  | 'error'
  | 'success'
  | 'disabled'

type Props = {
  /** Label to display in the Badge, also used as aria-label */
  label: string
  /** The size of the Badge. */
  size?: 'sm' | 'md' | 'lg'
  /** The specific style of Badge to render */
  variant?: BadgeVariant
}

/**
 * The `BaseBadge` is used to display high level information or status.
 *
 * This component is meant to be used internally in the component library. In consumer applications, the `Badge` component is available.
 */
const BaseBadge = ({
  label,
  size = 'lg',
  variant = 'primary',
  ...props
}: Props) => {
  return (
    <BootstrapBadge
      {...props}
      aria-label={label}
      className={`badge-${variant} badge-${size}`}
    >
      {label}
    </BootstrapBadge>
  )
}

export { BaseBadge, type Props as BaseBadgeProps }

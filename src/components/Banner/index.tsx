import clsx from 'clsx'
import * as Icons from 'react-bootstrap-icons'

type IconName = keyof typeof Icons

type Props = {
  /** Variant styles */
  variant: 'warning' | 'success'
  /** The main title of the banner. */
  title: string
  /** Banner text or instructions */
  text: string
  /** The icon to render within the button, if any.
   * Access https://icons.getbootstrap.com/ to see the list of icons.
   * The icon name should be passed as a string, using PascalCase convention e.g. 'PlusSquare' or 'ArrowLeftShort'.
   */
  icon?: IconName
  /** Additional banner actions. */
  children?: React.ReactNode
}

/**
 * The Banner component provides top of page full width messages for users. If you're looking for something inline, consider using the Alert component.
 */
const Banner = ({ variant, icon, title, text, children }: Props) => {
  const IconComponent = icon ? Icons[icon] : null

  return (
    <div className={clsx('banner', `banner-${variant}`)} role="alert">
      {IconComponent && <IconComponent className="icon" role="presentation" />}
      <h2>{title}</h2>
      <p>{text}</p>
      {children}
    </div>
  )
}

export { Banner }

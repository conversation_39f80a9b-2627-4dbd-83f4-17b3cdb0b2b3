import { HTMLProps } from 'react'

type Props = Omit<
  HTMLProps<HTMLTextAreaElement>,
  'type' | 'readOnly' | 'className'
>
/**
 * The ReadOnlyInput provides a read only value.
 * It is used with the NumberInput, TextInput, and ChoiceGroup component.
 * All of its props are passed from those components.
 */
const ReadOnlyInput = (props: Props) => {
  return (
    <textarea
      {...props}
      className="readonly-input"
      readOnly
      value={props.value ?? ''}
    />
  )
}

export { ReadOnlyInput }

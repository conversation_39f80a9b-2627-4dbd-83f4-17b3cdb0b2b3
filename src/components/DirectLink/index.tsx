import { AnchorHTMLAttributes, PropsWithChildren } from 'react'

type LinkProps = PropsWithChildren<AnchorHTMLAttributes<HTMLAnchorElement>> & {
  to: string
}

/**
 * DirectLink is a wrapper for a standard HTML anchor tag to grant parity with
 * various router components, like `react-router-dom`'s `Link` component. It
 * translates a passed `to` prop into an `href`, but otherwise passes other
 * props through to the element.
 */
export const DirectLink = ({ to, children, ...props }: LinkProps) => {
  return (
    <a href={to} {...props}>
      {children}
    </a>
  )
}

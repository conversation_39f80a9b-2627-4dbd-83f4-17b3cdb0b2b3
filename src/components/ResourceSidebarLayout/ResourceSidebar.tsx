import { Heading, Landmark } from '@/components/Content'
import { useI18n } from '@/i18n/useI18n'
import React, { TransitionEvent, useId } from 'react'
import { ChevronLeft, ChevronRight } from 'react-bootstrap-icons'
import { ResourceButton } from './ResourceButton'

type Props = {
  children?: React.ReactNode
  isOpen: boolean
  onToggle?: () => void
  onToggleEnd?: () => void
}

const ResourceSidebar = ({
  children,
  isOpen,
  onToggle,
  onToggleEnd,
}: Props) => {
  const { t } = useI18n('ResourcesSidebar')
  const sidebarId = useId()

  function handleTransitionStart(event: TransitionEvent<HTMLDivElement>) {
    const property = event.propertyName || event.nativeEvent.propertyName

    if (property === 'width') {
      event.currentTarget.setAttribute('data-animating', 'true')
    }
  }

  function handleTransitionEnd(event: TransitionEvent<HTMLDivElement>) {
    const property = event.propertyName || event.nativeEvent.propertyName

    if (property === 'width') {
      event.currentTarget.removeAttribute('data-animating')
      onToggleEnd?.()
    }
  }

  return (
    <div
      className="motion-safe:transition-[width_transform] motion-safe:aria-hidden:translate-x-full motion-safe:translate-x-0 group data-[open=true]:w-[300px] data-[open=false]:w-[56px] overflow-hidden"
      onTransitionStart={handleTransitionStart}
      onTransitionEnd={handleTransitionEnd}
      data-open={isOpen}
    >
      <Landmark
        className="resource-sidebar group-data-[animating=true]:w-[300px]"
        as="nav"
        id={sidebarId}
        level={2}
        aria-expanded={isOpen}
      >
        <header className="resource-sidebar-header">
          <Heading className="text-title-sm sr-only">
            Platform One Sidebar
          </Heading>
          <ResourceButton
            aria-controls={sidebarId}
            text={isOpen ? t('close') : t('open')}
            Icon={isOpen ? ChevronLeft : ChevronRight}
            onClick={onToggle}
            iconOnly
          />
        </header>
        {children}
      </Landmark>
    </div>
  )
}

export { ResourceSidebar }

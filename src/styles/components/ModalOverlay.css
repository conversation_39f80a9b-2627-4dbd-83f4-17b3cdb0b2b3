/**
* Base Component Definition
*/
.modal {
  --gatx-modal-zindex: 2000;
  --gatx-modal-body-bg-color: var(--gatx-colors-light-4);

  --gatx-modal-padding-h: var(--gatx-spacing-lg);
  --gatx-modal-padding-v: var(--gatx-spacing-md);
  --gatx-modal-margin: var(--gatx-spacing-lg);

  --gatx-modal-border-color: var(--gatx-colors-light-6);

  --gatx-modal-text-fg-color: var(--gatx-colors-dark-8);

  --gatx-modal-width: 50%;
  --gatx-modal-max-height: 500px;

  @media (prefers-color-scheme: dark) {
    --gatx-modal-body-bg-color: var(--gatx-colors-dark-7);
    --gatx-modal-text-fg-color: var(--gatx-colors-light-1);
    --gatx-modal-border-color: var(--gatx-colors-dark-5);
  }
  display: none;
  z-index: var(--gatx-modal-zindex);

  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  color: var(--gatx-modal-text-fg-color);

  .modal-dialog {
    display: flex;
    position: relative;
    width: auto;
    max-width: var(--gatx-modal-width);
    margin: var(--gatx-modal-margin);
    margin-right: auto;
    margin-left: auto;
    font-weight: var(--gatx-typography-font-weight-regular);

    font-family: var(--gatx-typography-font-family);
    pointer-events: none;

    .modal.fade & {
      transform: 0.3s ease-out;
    }
    .modal.show & {
      transform: none;
    }

    .modal.modal-static & {
      transform: scale(1.02);
    }
  }

  .modal-dialog-centered {
    display: flex;
    align-items: center;
    min-height: calc(100% - var(--gatx-modal-margin) * 2);
  }

  .modal-header {
    display: flex;
    align-items: center;
    width: 100%;
    min-height: 56px;
    font-weight: var(--gatx-typography-font-weight-medium);

    .btn-close {
      margin: 10px 24px;
      float: right;
    }
  }

  .modal-title {
    display: inline-block;
    padding: var(--gatx-modal-padding-v) var(--gatx-modal-padding-h);
  }

  .modal-body {
    overflow-y: auto;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: var(--gatx-modal-border-color);
  }

  .modal-footer {
    display: flex;
    justify-content: right;
    padding: var(--gatx-spacing-md) var(--gatx-spacing-lg);
    gap: var(--gatx-spacing-md);
  }

  .modal-content {
    display: flex;
    position: relative;
    flex-direction: column;
    width: 100%;
    max-height: var(--gatx-modal-max-height);
    border-radius: 5px;
    background-color: var(--gatx-modal-body-bg-color);
    pointer-events: auto;
  }

  .modal-fullscreen {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;

    .modal-content {
      height: 100%;
      max-height: 100%;
      border: 0;
      border-radius: 0;
    }

    .modal-header {
      border-radius: 0;
    }

    .modal-body {
      height: 100%;
      overflow-y: auto;
    }
  }
}

.modal-backdrop {
  --gatx-modal-zindex: 1;
  --gatx-modal-overlay-color: var(--gatx-colors-dark-10);
  --gatx-modal-overlay-opacity: 0.7;
  z-index: var(--gatx-modal-zindex);

  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: var(--gatx-modal-overlay-color);
  opacity: var(--gatx-modal-overlay-opacity);

  @media (prefers-color-scheme: dark) {
    --gatx-modal-overlay-color: var(--gatx-colors-dark-1);
  }
}

.modal-spinner {
  display: flex;
  z-index: 2;
  position: fixed;
  top: 50%;
  left: 50%;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
}

.spinner {
  color: var(--gatx-colors-light-1);

  @media (prefers-color-scheme: dark) {
    color: var(--gatx-colors-dark-8);
  }
}

/**
* Variants - Size
*/

.modal-sm {
  --gatx-modal-width: 30%;
  --gatx-modal-max-height: 400px;
}

.modal-lg {
  --gatx-modal-width: 80%;
  --gatx-modal-max-height: 600px;
}

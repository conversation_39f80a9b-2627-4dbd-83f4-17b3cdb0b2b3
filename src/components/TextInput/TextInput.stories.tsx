import { withActions } from '@storybook/addon-actions/decorator'
import type { Args, Meta, StoryObj } from '@storybook/react'
import { useState } from 'react'
import { TextInput } from '.'
import { FormField } from '../FormField'

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta: Meta = {
  title: 'Components/TextInput',
  component: TextInput,
  parameters: {
    layout: 'centered',
  },
  decorators: [(Story) => <div>{Story()}</div>, withActions],
}

export default meta
type Story = StoryObj<typeof meta>

export const TextInputStory: Story = {
  name: 'Default',
  render: (args: Args) => <TextInputFormField {...args} />,
  parameters: {
    docs: {
      description: {
        story: 'TextInput component for form fields',
      },
    },
  },
}

const TextInputFormField = () => {
  const [value, setValue] = useState<string>()

  return (
    <FormField label="Text Input">
      <TextInput value={value} onChange={setValue} placeholder="Enter text" />
    </FormField>
  )
}

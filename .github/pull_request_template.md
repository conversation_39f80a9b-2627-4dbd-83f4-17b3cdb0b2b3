<!-- Ensure that your PR title includes a tag, the story number, and a descriptive name. -->

<!-- `BREAKING CHANGE` PRs **must** include `BREAKING CHANGE: [description]` in the PR description or commit footer. The PR title should include a tag that closest aligns with the changes. -->
<!-- Version changing tags are `feat` and `fix`. -->

<!-- Other non version changing tags are `chore`, `docs`, `refactor`, and `test`. -->

<!-- It is recommended to scope your PR `feat(scope)` as this improves the Release Notes. -->

## Reference Links

<!-- For any story work, replace the `{STORY_NUMBER}` in the URL below with the appropriate value. -->
<!-- If your change is just a refactor or configuration change, feel free to remove this link. -->

- [Story Link](https://gatx-corp.atlassian.net/browse/{STORY_NUMBER})

<!-- If you're implementing a designed interface, link to the appropriate Figma view here. -->
<!-- Otherwise, feel free to remove this link. -->

- [Figma Link](https://www.figma.com/{DESIGN_URL})

## Summary

<!-- Why is this change required? What feature does it add, or what problem does it solve? -->
<!-- Give a quick overview of the approach you took, and the rationale behind any changes. -->

## Checklist

- [ ] I've added any new atomic components to Storybook
- [ ] I've written top-level JSDoc documentation for any new functions or components
- [ ] I've added or updated tests to verify the behavior of my change
- [ ] I've performed at least a cursory manual keyboard accessibility check
- [ ] I've performed at least a cursory manual screen reader accessibility check

## Screenshots

<!-- If you've implemented anything visual, include a screenshot here. Otherwise, feel free to delete this section. -->

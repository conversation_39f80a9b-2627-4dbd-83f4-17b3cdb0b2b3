import { render, screen } from '@/tests/react'
import userEvent from '@testing-library/user-event'
import { axe } from 'jest-axe'
import { Button } from '.'

describe('Button', () => {
  test('handles click events on an accessible button', async () => {
    const onClick = jest.fn()

    render(<Button text="My Button Text" variant="primary" onClick={onClick} />)
    await userEvent.click(
      screen.getByRole('button', { name: 'My Button Text' }),
    )

    expect(onClick).toHaveBeenCalled()
  })

  test('adheres to accessibility guidelines', async () => {
    const { container } = render(
      <Button text="My Button Text" variant="primary" />,
    )
    expect(await axe(container)).toHaveNoViolations()
  })
})

describe('Icon Button', () => {
  test('renders icon button with correct text and icon', () => {
    render(<Button text="My Icon Button" icon="Plus" />)

    const button = screen.getByRole('button', { name: 'My Icon Button' })

    expect(button).toBeInTheDocument()
    // Check if icon exists and is hidden from screen readers
    const icon = button.querySelector('svg')
    expect(icon).toHaveAttribute('aria-hidden', 'true')
  })

  test('adheres to accessibility guidelines', async () => {
    const { container } = render(<Button text="My Icon Button" icon="Plus" />)
    expect(await axe(container)).toHaveNoViolations()
  })
})

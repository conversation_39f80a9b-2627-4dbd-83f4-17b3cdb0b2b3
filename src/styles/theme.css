html,
body {
  height: 100%;
  overflow: clip;
  background-color: var(--gatx-color-bg);

  & * {
    scrollbar-color: var(--gatx-scrollbar-thumb) transparent;
    scrollbar-width: thin;

    @media (prefers-reduced-motion: reduce) {
      transition: none;
    }
  }
}

:root {
  --gatx-color-bg: var(--gatx-colors-light-6);
  --gatx-color-primary: var(--color-primary);
  --gatx-color-primary-dark: var(--color-primary-dark);
  --gatx-scrollbar-thumb: var(--gatx-colors-light-8);

  @media (prefers-color-scheme: dark) {
    --gatx-color-bg: var(--gatx-colors-dark-8);
    --gatx-color-primary: var(--gatx-colors-primary-5);
    --gatx-color-primary-dark: var(--gatx-colors-primary-7);
    --gatx-scrollbar-thumb: var(--gatx-colors-dark-2);
  }

  --gatx-colors-primary-1: var(--color-primary-1);
  --gatx-colors-primary-2: var(--color-primary-2);
  --gatx-colors-primary-3: var(--color-primary-3);
  --gatx-colors-primary-4: var(--color-primary-4);
  --gatx-colors-primary-5: var(--color-primary-5);
  --gatx-colors-primary-6: var(--color-primary-6);
  --gatx-colors-primary-7: var(--color-primary-7);
  --gatx-colors-primary-8: var(--color-primary-8);
  --gatx-colors-primary-9: var(--color-primary-9);
  --gatx-colors-primary-10: var(--color-primary-10);

  --gatx-colors-secondary-1: var(--color-secondary-1);
  --gatx-colors-secondary-2: var(--color-secondary-2);
  --gatx-colors-secondary-3: var(--color-secondary-3);
  --gatx-colors-secondary-4: var(--color-secondary-4);
  --gatx-colors-secondary-5: var(--color-secondary-5);
  --gatx-colors-secondary-6: var(--color-secondary-6);
  --gatx-colors-secondary-7: var(--color-secondary-7);
  --gatx-colors-secondary-8: var(--color-secondary-8);
  --gatx-colors-secondary-9: var(--color-secondary-9);
  --gatx-colors-secondary-10: var(--color-secondary-10);

  --gatx-colors-tertiary-1: var(--color-tertiary-1);
  --gatx-colors-tertiary-2: var(--color-tertiary-2);
  --gatx-colors-tertiary-3: var(--color-tertiary-3);
  --gatx-colors-tertiary-4: var(--color-tertiary-4);
  --gatx-colors-tertiary-5: var(--color-tertiary-5);
  --gatx-colors-tertiary-6: var(--color-tertiary-6);
  --gatx-colors-tertiary-7: var(--color-tertiary-7);
  --gatx-colors-tertiary-8: var(--color-tertiary-8);
  --gatx-colors-tertiary-9: var(--color-tertiary-9);
  --gatx-colors-tertiary-10: var(--color-tertiary-10);

  --gatx-colors-light-1: var(--color-light-1);
  --gatx-colors-light-2: var(--color-light-2);
  --gatx-colors-light-3: var(--color-light-3);
  --gatx-colors-light-4: var(--color-light-4);
  --gatx-colors-light-5: var(--color-light-5);
  --gatx-colors-light-6: var(--color-light-6);
  --gatx-colors-light-7: var(--color-light-7);
  --gatx-colors-light-8: var(--color-light-8);
  --gatx-colors-light-9: var(--color-light-9);
  --gatx-colors-light-10: var(--color-light-10);

  --gatx-colors-dark-1: var(--color-dark-1);
  --gatx-colors-dark-2: var(--color-dark-2);
  --gatx-colors-dark-3: var(--color-dark-3);
  --gatx-colors-dark-4: var(--color-dark-4);
  --gatx-colors-dark-5: var(--color-dark-5);
  --gatx-colors-dark-6: var(--color-dark-6);
  --gatx-colors-dark-7: var(--color-dark-7);
  --gatx-colors-dark-8: var(--color-dark-8);
  --gatx-colors-dark-9: var(--color-dark-9);
  --gatx-colors-dark-10: var(--color-dark-10);

  --gatx-colors-error-1: var(--color-error-1);
  --gatx-colors-error-2: var(--color-error-2);
  --gatx-colors-error-3: var(--color-error-3);
  --gatx-colors-error-4: var(--color-error-4);
  --gatx-colors-error-5: var(--color-error-5);
  --gatx-colors-error-6: var(--color-error-6);
  --gatx-colors-error-7: var(--color-error-7);
  --gatx-colors-error-8: var(--color-error-8);
  --gatx-colors-error-9: var(--color-error-9);
  --gatx-colors-error-10: var(--color-error-10);

  --gatx-colors-warning-1: var(--color-warning-1);
  --gatx-colors-warning-2: var(--color-warning-2);
  --gatx-colors-warning-3: var(--color-warning-3);
  --gatx-colors-warning-4: var(--color-warning-4);
  --gatx-colors-warning-5: var(--color-warning-5);
  --gatx-colors-warning-6: var(--color-warning-6);
  --gatx-colors-warning-7: var(--color-warning-7);
  --gatx-colors-warning-8: var(--color-warning-8);
  --gatx-colors-warning-9: var(--color-warning-9);
  --gatx-colors-warning-10: var(--color-warning-10);

  --gatx-colors-success-1: var(--color-success-1);
  --gatx-colors-success-2: var(--color-success-2);
  --gatx-colors-success-3: var(--color-success-3);
  --gatx-colors-success-4: var(--color-success-4);
  --gatx-colors-success-5: var(--color-success-5);
  --gatx-colors-success-6: var(--color-success-6);
  --gatx-colors-success-7: var(--color-success-7);
  --gatx-colors-success-8: var(--color-success-8);
  --gatx-colors-success-9: var(--color-success-9);
  --gatx-colors-success-10: var(--color-success-10);

  --gatx-border-radius: 4px;

  --gatx-spacing-xs: var(--spacing-xs);
  --gatx-spacing-sm: var(--spacing-sm);
  --gatx-spacing-md: var(--spacing-md);
  --gatx-spacing-lg: var(--spacing-lg);
  --gatx-spacing-xl: var(--spacing-xl);
  --gatx-spacing-2xl: var(--spacing-2xl);
}

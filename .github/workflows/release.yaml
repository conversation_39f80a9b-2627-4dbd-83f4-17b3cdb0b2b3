name: Test, Build & Release

on:
  workflow_dispatch: {}

permissions:
  contents: write
  pull-requests: write
  issues: write
  packages: write

jobs:
  Release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1

      - name: Install dependencies
        run: bun install

      - name: Build project
        run: bun run build

      - name: Run tests
        run: bun run test

      - name: Release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: bunx semantic-release

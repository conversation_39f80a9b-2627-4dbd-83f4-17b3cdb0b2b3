import { Alert } from '@/components/Alert'
import { ResourceSidebarLayout } from '@/components/ResourceSidebarLayout'
import { PropsWithChildren } from 'react'
import { useApplication } from './useApplication'

const HOME_APPLICATION = 'PLATFORM_ONE'

const ResourcesProvider = ({ children }: PropsWithChildren) => {
  const { application, user } = useApplication()

  const apps = user.authorizations.applications ?? []
  const applications = apps.filter((app) => app.code !== HOME_APPLICATION)
  const homeApplication = apps.find((app) => app.code === HOME_APPLICATION)

  const hasAccess = apps.some((app) => app.code === application)

  return (
    <ResourceSidebarLayout
      applications={applications}
      homeApplication={homeApplication}
      resources={user.resources ?? []}
    >
      {hasAccess ? (
        children
      ) : (
        <Alert
          compact
          level="warning"
          urgency="inline"
          title="No Access"
          className="m-xl"
        >
          You do not have permissions for application {application}. If you
          believe you should be granted access, please contact your systems
          administrator.
        </Alert>
      )}
    </ResourceSidebarLayout>
  )
}

export { ResourcesProvider }

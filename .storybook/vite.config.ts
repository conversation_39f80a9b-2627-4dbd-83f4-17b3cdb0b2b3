import tailwindcss from '@tailwindcss/vite'
import react from '@vitejs/plugin-react'
import { defineConfig } from 'vite'
import postcssGATXPlatformOneCommon from '../src/postcss'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss()],
  resolve: {
    alias: {
      '@': '/src',
      'next-auth/react': '/src/auth/next-auth.mock.tsx',
    },
  },
  css: {
    postcss: {
      plugins: [postcssGATXPlatformOneCommon()],
    },
  },
})

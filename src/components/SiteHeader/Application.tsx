import { ElementType } from 'react'
import { DirectLink } from '../DirectLink'
import { Logo } from './Logo'

type Props = {
  name?: string
  Link?: ElementType
}

const Application = ({ name, Link = DirectLink }: Props) => {
  return (
    <header className="text-(--gatx-typography-color-text) flex flex-row items-center space-x-sm">
      <Link to="/" aria-label={`${name ?? 'Application'} Home`}>
        <Logo className="text-primary dark:text-(--gatx-typography-color-text)" />
      </Link>
      <span className="flex flex-col border-s border-(--gatx-typography-color-text) pl-sm">
        <span className="text-[16px] leading-[18px] font-[400]">
          Platform One
        </span>
        <span className="text-[9px] leading-[9px] font-[700] uppercase">
          {name ?? ''}
        </span>
      </span>
    </header>
  )
}

export { Application }

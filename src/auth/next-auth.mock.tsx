import { PropsWithChildren } from 'react'
import { User } from './types/User'

const PLACEHOLDER_PERMISSIONS = {
  applications: [
    {
      code: 'PLATFORM_ONE_SHOP_PORTAL',
      name: 'Perform Inspection',
      description: 'Document and review car data',
      uri: 'https://platformoneproj1.dev.gatxrail.com',
    },
  ],

  metadata: {
    PLATFORM_ONE_SHOP_PORTAL: {},
  },

  authorizations: {
    PLATFORM_ONE_SHOP_PORTAL: [
      {
        scope: null,
        permissions: [],
      },
    ],
  },
}

export const PLACEHOLDER_USER: User = {
  id: 'CHLUNDY',
  name: '<PERSON>',
  email: '<EMAIL>',
  external: false,
  authorizations: PLACEHOLDER_PERMISSIONS,
  resources: [
    {
      title: 'Link 1',
      href: 'https://platformone.com',
    },
    {
      title: 'Link 2',
      href: 'https://platformone.com',
    },
    {
      title: 'Link 3',
      href: 'https://platformone.com',
    },
  ],
}

export const SessionProvider = ({ children }: PropsWithChildren) => {
  return <>{children}</>
}

export const useSession = () => {
  return {
    data: {
      user: PLACEHOLDER_USER,
    },
  }
}

export const signOut = () => {
  return Promise.resolve()
}

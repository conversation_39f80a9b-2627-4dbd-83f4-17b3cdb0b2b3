import { Button } from '@/components/Button'
import { ModalOverlay } from '@/components/ModalOverlay'
import { useI18n } from '@/i18n/useI18n'
import { HTMLAttributes } from 'react'

type FooterProps = HTMLAttributes<HTMLDivElement> & {
  onConfirmLogout: () => void
  onCancel: () => void
}

const Footer = ({
  onConfirmLogout: onConfirmLogout,
  onCancel,
}: FooterProps) => {
  return (
    <div className="flex justify-end gap-sm">
      <Button text="Cancel" variant="outline" onClick={onCancel} />
      <Button
        text="Log Out"
        onClick={() => {
          onConfirmLogout()
        }}
      />
    </div>
  )
}

type Props = HTMLAttributes<HTMLDivElement> & {
  onLogout: () => void
  onCancel: () => void
  show: boolean
}

const LogoutConfirmationModal = ({
  onLogout: onLogout,
  onCancel,
  show,
}: Props) => {
  const { t } = useI18n('SiteHeader')
  return (
    <ModalOverlay
      onHide={onCancel}
      show={show}
      size="small"
      title={t('logout')}
      footer={<Footer onConfirmLogout={() => onLogout()} onCancel={onCancel} />}
    >
      <p
        aria-labelledby="form-description"
        className="justify-center mt-sm mb-xl p-lg"
      >
        {t('confirmationQuestion')}
      </p>
    </ModalOverlay>
  )
}

export { LogoutConfirmationModal }

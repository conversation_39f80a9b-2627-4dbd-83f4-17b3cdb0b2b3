'use client'

import { ResourcesProvider } from '@/auth/ResourcesProvider'
import { User } from '@/auth/types/User'
import { i18n as i18WithConfig } from '@/i18n/i18n'
import { useI18n as useTranslationWrapper } from '@/i18n/useI18n'
import { ColorSchemeProvider } from '@/stores/ColorScheme/ColorSchemeProvider'
import { Session } from 'next-auth'
import { SessionProvider, useSession } from 'next-auth/react'
import { createContext, PropsWithChildren, useEffect } from 'react'
import { useLocalStorage } from './stores/LocalStorage'
import { WijmoLicenseProvider } from './wijmo'

import type { i18n as I18nInstance } from 'i18next'
import type { useTranslation } from 'react-i18next'

export const i18n: I18nInstance = i18WithConfig
export const useI18n: typeof useTranslation = useTranslationWrapper
export type { enResources as DefaultResources } from './i18n/resources/en'

type Props = PropsWithChildren & {
  application: string
  defaultUser?: User
  disableAuth?: boolean
  wijmoLicense?: string
}

type PlatformOneData = {
  application: string
  user: User
}

const SESSION_KEY = 'session'

export const PlatformOneContext = createContext<PlatformOneData | undefined>(
  undefined,
)

const UserProvider = ({
  application,
  defaultUser,
  children,
  disableAuth,
}: Omit<Props, 'wijmoLicense'>) => {
  // We use local storage to persist the user's session locally, so the user can
  // continue to access the application even while offline.
  const [, setOfflineSession] = useLocalStorage<Session | null>(
    SESSION_KEY,
    null,
  )
  const { data: session } = useSession()

  useEffect(() => {
    setOfflineSession(session)
  }, [session, setOfflineSession])

  const user = disableAuth ? defaultUser : session?.user

  return (
    user && (
      <PlatformOneContext.Provider value={{ application, user }}>
        {children}
      </PlatformOneContext.Provider>
    )
  )
}

const PlatformOneProvider = ({
  application,
  defaultUser,
  disableAuth,
  wijmoLicense = '',
  children,
}: Props) => {
  // This value gets populated in the UserProvider above, after a successful
  // authentication.
  const [offlineSession] = useLocalStorage<Session | null>(SESSION_KEY, null)

  return (
    <WijmoLicenseProvider license={wijmoLicense}>
      <SessionProvider
        session={navigator.onLine ? undefined : offlineSession}
        refetchOnWindowFocus={false}
        refetchWhenOffline={false}
      >
        <UserProvider
          application={application}
          defaultUser={defaultUser}
          disableAuth={disableAuth}
        >
          <ColorSchemeProvider>
            <ResourcesProvider>{children}</ResourcesProvider>
          </ColorSchemeProvider>
        </UserProvider>
      </SessionProvider>
    </WijmoLicenseProvider>
  )
}

export { PlatformOneProvider }

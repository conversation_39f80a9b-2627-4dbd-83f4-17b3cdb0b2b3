import { useI18n } from '@/i18n/useI18n'
import { Spinner as BootstrapSpinner } from 'react-bootstrap'
const SIZE_MAPPINGS = { small: 'sm', medium: undefined } as const

type Size = keyof typeof SIZE_MAPPINGS

type Props = {
  /** Whether the spinner should be hidden from screen readers. A reason to do this would be if you want to treat it as a presentational icon vs. a loading indicator. */
  ariaHidden?: boolean
  /** The size of the spinner. */
  size?: Size
  /** Additional classes to apply to the spinner. */
  className?: string
}

const Spinner = ({ ariaHidden = false, size = 'small', className }: Props) => {
  const { t } = useI18n()
  return (
    <BootstrapSpinner
      className={className}
      aria-hidden={ariaHidden}
      animation="border"
      size={SIZE_MAPPINGS[size]}
      role="status"
    >
      <span className="sr-only">{t('loading')}...</span>
    </BootstrapSpinner>
  )
}

export { Spinner }

/**
 Base Component
 */

.side-panel {
  --gatx-side-panel-padding: var(--gatx-spacing-lg);
  --gatx-side-panel-header-bg-color: var(--gatx-colors-light-3);

  display: flex;
  flex-direction: column;

  height: 100%;
  background-color: white;

  box-shadow: #00000015 0px 8px 16px 0;

  @media (prefers-color-scheme: dark) {
    --gatx-side-panel-header-bg-color: var(--gatx-colors-dark-6);
    background-color: var(--gatx-colors-dark-7);
  }
}

.side-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0;
  padding: var(--gatx-side-panel-padding);
  padding-bottom: 48px;
  gap: var(--gatx-spacing-xs);

  background-color: var(--gatx-side-panel-header-bg-color);
}

.side-panel-controls {
  display: flex;

  flex-wrap: wrap-reverse;
  align-items: center;
  justify-content: flex-end;
  gap: var(--gatx-spacing-xs);
}

.high-zoom {
  .side-panel-controls fieldset {
    display: none;
  }
}

.side-panel-content {
  min-height: 0;
  padding: var(--gatx-side-panel-padding);

  overflow-y: auto;

  &:has(.tabs-underline:first-child) {
    display: flex;
    flex-direction: column;

    padding: 0;
    overflow-y: clip;
    scrollbar-gutter: stable;

    > :not(.tabs-underline):not(.tab-content) {
      padding: var(--gatx-side-panel-padding);
    }

    > .tabs-underline:not(:first-child) {
      padding: 0 var(--gatx-side-panel-padding);
    }

    > .tab-content:first-of-type {
      overflow-y: auto;
    }
  }

  > .tabs-underline:first-child {
    padding: 2px var(--gatx-side-panel-padding);
    border-bottom: unset;
    background-color: var(--gatx-side-panel-header-bg-color);
  }

  > .tab-content {
    padding-bottom: 20px;
    padding: var(--gatx-side-panel-padding);
    padding-right: 8px;
    scrollbar-gutter: stable;
  }
}

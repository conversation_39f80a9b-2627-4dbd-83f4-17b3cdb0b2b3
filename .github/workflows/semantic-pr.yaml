name: Validate PR title

on:
  pull_request_target:
    types:
      - opened
      - edited
      - reopened
      - synchronize

permissions:
  pull-requests: read
  statuses: write

jobs:
  semantic-pr:
    runs-on: ubuntu-latest
    steps:
      - uses: amannn/action-semantic-pull-request@v5
        with:
          types: |
            feat
            fix
            chore
            docs
            refactor
            test
          requireScope: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

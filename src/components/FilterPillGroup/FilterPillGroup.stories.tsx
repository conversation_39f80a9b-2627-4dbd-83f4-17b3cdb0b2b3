import { withActions } from '@storybook/addon-actions/decorator'
import type { Meta, StoryObj } from '@storybook/react'
import { FilterPillGroup } from './index'

const meta: Meta<typeof FilterPillGroup> = {
  title: 'Components/FilterPillGroup',
  component: FilterPillGroup,
  parameters: {
    layout: 'padded',
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    appliedFilters: [
      {
        value: 'material',
        text: 'Material Type',
        filterSource: 'materialType',
      },
      {
        value: 'manufacturer-01',
        text: 'Manufacturer 1',
        filterSource: 'manufacturerType',
      },
      {
        value: 'manufacturer-2',
        text: 'Manufacturer 2',
        filterSource: 'manufacturerType',
      },
    ],
    removeFilter: (value: string, filterSource: string) =>
      console.log(value, filterSource),
  },
  parameters: {
    layout: 'centered',
    actions: { handles: ['click'] },
  },
  decorators: [withActions],
}

.search-bar {
  --gatx-search-bar-bg: var(--gatx-colors-light-2);
  --gatx-search-bar-color: var(--gatx-colors-dark-8);

  --gatx-search-bar-border-radius: 1px;
  --gatx-search-bar-focus-color: var(--gatx-colors-primary-4);
  --gatx-search-bar-outline-color: var(--gatx-colors-primary-7);
  --gatx-search-bar-disabled-color: var(--gatx-colors-light-8);
  --gatx-search-bar-button-width: 48px;

  @media (prefers-color-scheme: dark) {
    --gatx-search-bar-bg: var(--gatx-colors-dark-9);
    --gatx-search-bar-color: var(--gatx-colors-light-1);
    --gatx-search-bar-focus-color: var(--gatx-colors-secondary-3);
    --gatx-search-bar-outline-color: var(--gatx-colors-secondary-5);

    --gatx-search-bar-disabled-color: var(--gatx-colors-dark-4);
  }

  .wj-control {
    background: transparent;
  }

  .wj-content {
    overflow: hidden;
    border: none;
    border-radius: var(--gatx-border-radius);
  }

  .wj-state-focused:not([disabled]) {
    border-radius: var(--gatx-border-radius);
    outline: 1px solid var(--gatx-search-bar-outline-color);
  }

  .wj-template {
    height: inherit;
    border-radius: var(--gatx-border-radius);
  }

  .wj-form-control {
    width: calc(100% - var(--gatx-search-bar-button-width));
    height: inherit;
    padding: 10px;
    background: var(--gatx-search-bar-bg);
    color: var(--gatx-search-bar-color);

    &:focus {
      outline: none;
    }

    &:disabled {
      color: var(--gatx-search-bar-disabled-color);
      pointer-events: none;
    }
  }

  .wj-input-group {
    height: inherit;
    border: none;
    background: var(--gatx-search-bar-bg);
  }

  .wj-input-group-btn {
    background: var(--gatx-search-bar-bg);
    cursor: pointer;

    .wj-btn-default {
      background: var(--gatx-search-bar-bg);
    }
  }

  .wj-input {
    height: inherit;
  }

  .wj-btn {
    width: var(--gatx-search-bar-button-width);
  }

  .wj-dropdown-panel {
    background: var(--gatx-search-bar-bg);
  }

  .wj-btn-default {
    background: var(--gatx-search-bar-bg);
  }

  .wj-input-group-btn > .wj-btn {
    background: var(--gatx-search-bar-bg);
  }

  .wj-glyph-down {
    display: none;
  }

  .wj-state-disabled {
    opacity: 0.9;
  }

  .wj-listbox {
    background: var(--gatx-search-bar-bg);
  }

  .search-bar-icon {
    z-index: 1;
    margin-left: -30px;
    color: var(--gatx-search-bar-color);
    cursor: pointer;
    pointer-events: none;
  }

  .search-bar-icon-disabled {
    color: var(--gatx-search-bar-disabled-color);
  }

  .search-bar-spinner {
    width: 16px;
    height: 16px;
    margin-left: -30px;
    color: var(--gatx-search-bar-color);
    cursor: default;
  }
}

.wj-dropdown-panel {
  --gatx-listbox-bg: var(--gatx-colors-light-2);
  @media (prefers-color-scheme: dark) {
    --gatx-listbox-bg: var(--gatx-colors-dark-9);
  }

  max-height: 250px;
  margin-top: 4px;
  overflow-y: auto;
  border: none;
  border-radius: 4px;
  background: var(--gatx-listbox-bg);
}

.wj.listbox {
  width: 500px;
  background: var(--gatx-listbox-bg);
}

.wj-listbox-item {
  --gatx-listbox-bg: var(--gatx-colors-light-2);
  --gatx-listbox-color: var(--gatx-colors-dark-5);
  --gatx-search-bar-focus-color: var(--gatx-colors-dark-8);
  --gatx-search-bar-focus-bg: var(--gatx-colors-light-3);
  --gatx-search-bar-outline-color: var(--gatx-colors-primary-7);

  --gatx-search-bar-border-radius: 4.8px;

  @media (prefers-color-scheme: dark) {
    --gatx-listbox-bg: var(--gatx-colors-dark-9);
    --gatx-listbox-color: var(--gatx-colors-dark-2);
    --gatx-search-bar-focus-color: var(--gatx-colors-light-1);
    --gatx-search-bar-focus-bg: var(--gatx-colors-dark-10);
    --gatx-search-bar-outline-color: var(--gatx-colors-secondary-5);
  }

  padding: 8px;
  background: var(--gatx-listbox-bg);
  color: var(--gatx-listbox-color);
  cursor: pointer;

  &:hover {
    background: var(--gatx-search-bar-focus-bg);
    color: var(--gatx-search-bar-focus-color);
  }

  &.wj-state-selected {
    border-radius: var(--gatx-search-bar-border-radius);
    outline: 4px solid
      color-mix(in srgb, var(--gatx-search-bar-outline-color), transparent 40%);
    outline-offset: -4px;
    background: var(--gatx-search-bar-focus-bg);
    color: var(--gatx-search-bar-focus-color);
  }
}

.search-bar-item {
  height: 20px;
}

.search-bar-empty-message {
  padding: 0 var(--gatx-spacing-xs);
  font-size: var(--gatx-typography-font-size-sm);
}

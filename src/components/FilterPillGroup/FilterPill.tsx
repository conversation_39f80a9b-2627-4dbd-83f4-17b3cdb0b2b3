import { useI18n } from '@/i18n/useI18n'
import { XLg } from 'react-bootstrap-icons'

export type FilterPillProps = {
  /** Value for the applied filter */
  value: string
  /** Text to display in the pill */
  text: string
  /** The source of the filter pill. This should be the category or input that the filter option belongs to. */
  filterSource: string
}

type Props = FilterPillProps & {
  /** Callback to remove the filter */
  removeFilter: (value: string, filterSource: string) => void
}

/**
 * FilterPill component to display a select filter with an option to remove them. Used as part of the FilterPillGroup.
 */

export const FilterPill = ({
  value,
  text,
  filterSource,
  removeFilter,
}: Props) => {
  const { t } = useI18n('FilterPill')
  return (
    <button
      className="filter-pill"
      type="button"
      onClick={() => removeFilter(value, filterSource)}
      aria-label={t('remove', { text })}
    >
      {text}
      <XLg className="icon" aria-hidden />
    </button>
  )
}

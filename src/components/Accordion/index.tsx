import { useI18n } from '@/i18n/useI18n'
import { createEventKey } from '@/utils/createEventKey'
import clsx from 'clsx'
import {
  Children,
  HTMLAttributes,
  isValidElement,
  ReactElement,
  useState,
} from 'react'
import { Accordion as BootstrapAccordion } from 'react-bootstrap'
import { AccordionEventKey } from 'react-bootstrap/esm/AccordionContext'
import { Button } from '../Button'
import { Item } from './AccordionItem'

type PaneProps = {
  title: string
  disabled?: boolean
}

type Props = HTMLAttributes<HTMLDivElement> & {
  /** The specific style of button to render. See below for supported styles and their intended uses. */
  variant?: 'flush' | 'card'
  /** A collection of accordion panels to render. */
  children: ReactElement<PaneProps> | (ReactElement<PaneProps> | false)[]
  /** Whether the first accordion should be open by default. */
  expandFirstItem?: boolean
  /** Whether the accordion has expand/collapse buttons. */
  hasExpandCollapse?: boolean
  /** Context (text) to prepend to ScreenReader announcements for common elements of the container */
  screenReaderContext?: string
}

/**
 * The Accordion component is used to condense the display of pieces of
 * information, in an effort to make effective use of limited screen real estate
 * and allow the user to interact with only what is necessary to them at the
 * time.
 *
 * The component consists of two elements that can be composed together:
 *
 * - `Accordion.Container` is a parent element that provides context for the
 *   content contained within.
 * - `Accordion.Item` is a child element that contains the information for each
 *   accordion block.
 */
export const Container = ({
  variant = 'flush',
  children,
  expandFirstItem = false,
  hasExpandCollapse = false,
  screenReaderContext = '',
  ...props
}: Props) => {
  const { t } = useI18n('Accordion')
  const eventKeys = Children.map(children, (element) => {
    if (!isValidElement(element)) return

    const { title, disabled } = element.props
    return disabled ? undefined : createEventKey(title)
  }).filter(Boolean)

  const [activeKeys, setActiveKeys] = useState<string[]>(
    expandFirstItem ? [eventKeys[0]] : [],
  )

  const enabledActiveKeys = activeKeys.filter((k) => eventKeys.includes(k))

  const collapseAll = () => {
    setActiveKeys([])
  }

  const expandAll = () => {
    setActiveKeys(eventKeys)
  }

  const onSelect = (eventKey: AccordionEventKey) => {
    setActiveKeys(!eventKey ? [] : [eventKey].flat())
  }

  const collapseButtonText = t('collapseAll')
  const expandButtonText = t('expandAll')

  return (
    <>
      {hasExpandCollapse && (
        <div className="accordion-actions">
          <Button
            aria-label={`${screenReaderContext} ${collapseButtonText}`}
            onClick={collapseAll}
            disabled={!enabledActiveKeys || enabledActiveKeys.length === 0}
            variant="text"
            text={collapseButtonText}
          />
          <Button
            aria-label={`${screenReaderContext} ${expandButtonText}`}
            onClick={expandAll}
            disabled={enabledActiveKeys?.length === eventKeys.length}
            variant="text"
            text={expandButtonText}
          />
        </div>
      )}
      <BootstrapAccordion
        className={clsx(`accordion-${variant}`, props.className)}
        alwaysOpen
        activeKey={enabledActiveKeys}
        onSelect={onSelect}
      >
        {children}
      </BootstrapAccordion>
    </>
  )
}

const Accordion = { Container, Item }

export { Item } from './AccordionItem'
export { Accordion }

import { render } from '@/tests/react'
import { axe } from 'jest-axe'
import { DropDown } from '.'

describe('DropDown', () => {
  test('adheres to accessibility guidelines', async () => {
    const { container } = render(
      <DropDown.ToggleMenu label="Hello">
        <DropDown.Item href="/home" label="Home" key="home" />
        <DropDown.Item
          onClick={() => console.log('Clicked!')}
          label="Signout"
          key="signout"
        />
      </DropDown.ToggleMenu>,
    )
    expect(await axe(container)).toHaveNoViolations()
  })
})

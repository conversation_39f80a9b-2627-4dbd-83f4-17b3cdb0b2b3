/**
* Base Component Definition
*/
.accordion {
  --gatx-accordion-text-color: unset;
  --gatx-accordion-disabled-text-color: unset;
  --gatx-accordion-bg-color: unset;
  --gatx-accordion-body-bg-color: unset;
  --gatx-accordion-border-color: unset;
  --gatx-accordion-border-radius: unset;
  --gatx-accordion-box-shadow: unset;
  --gatx-accordion-space-y: unset;
  --gatx-accordion-icon-size: 1.25rem;

  --gatx-accordion-body-padding-x: unset;
  --gatx-accordion-body-padding-y: unset;
  --gatx-accordion-button-padding: unset;

  --gatx-accordion-active-bg-color: unset;
  --gatx-accordion-hover-bg-color: unset;
  --gatx-accordion-focus-bg-color: unset;
  --gatx-accordion-disabled-bg-color: unset;

  --gatx-accordion-outline-base-color: var(--gatx-colors-primary-7);

  @media (prefers-color-scheme: dark) {
    --gatx-accordion-outline-base-color: var(--gatx-colors-secondary-5);
  }
}

.collapse {
  &:not(.show) {
    display: none;
  }
}

.collapsing {
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;

  @media (prefers-reduced-motion: reduce) {
    transition: none;
  }
}

.accordion-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: var(--gatx-accordion-button-padding);

  background-color: var(--gatx-accordion-active-bg-color);
  color: var(--gatx-accordion-text-color);
  font-weight: var(--gatx-typography-font-weight-regular);
  font-size: var(--gatx-typography-font-size-regular);
  line-height: 1.2;
  text-align: left;

  white-space: nowrap;
  transition:
    box-shadow 0.15s ease-in-out,
    background-color 0.15s ease-in-out;

  .icon {
    flex-shrink: 0;
    order: 2;
    width: var(--gatx-accordion-icon-size);
    height: var(--gatx-accordion-icon-size);
    margin-left: auto;
    transition: transform 0.2s ease-in-out;
    @media (prefers-reduced-motion: reduce) {
      transition: none;
    }
  }

  &.collapsed {
    background-color: var(--gatx-accordion-bg-color);
  }

  &:not(.collapsed) {
    box-shadow: inset 0px -1px 0px var(--gatx-accordion-border-color);

    .icon {
      transform: rotate(-180deg);
    }
  }

  &:hover {
    background-color: var(--gatx-accordion-hover-bg-color);
  }

  &:focus-visible {
    z-index: 1;
    position: relative;
    border-radius: var(--gatx-border-radius);
    outline: none;
    background-color: var(--gatx-accordion-focus-bg-color);
    box-shadow: 0 0 0 4px
      color-mix(
        in srgb,
        var(--gatx-accordion-outline-base-color),
        transparent 40%
      );
  }

  &:disabled {
    background-color: var(--gatx-accordion-disabled-bg-color);
    color: var(--gatx-accordion-disabled-text-color);
  }

  @media (prefers-reduced-motion: reduce) {
    transition: none;
  }
}

.accordion-header {
  margin-bottom: 0;
}

.accordion-item {
  overflow: clip;
  border-bottom: 1px solid var(--gatx-accordion-border-color);

  border-radius: var(--gatx-accordion-border-radius);
  box-shadow: var(--gatx-accordion-box-shadow);

  &:not(:first-of-type) {
    margin-top: var(--gatx-accordion-space-y);
    border-top: 0;
  }
}

.accordion-body {
  padding: var(--gatx-accordion-body-padding-y)
    var(--gatx-accordion-body-padding-x);

  background-color: var(--gatx-accordion-body-bg-color);
}

/**
* Variants - Style
*/

.accordion-flush {
  --gatx-accordion-border-color: var(--gatx-colors-light-7);
  --gatx-accordion-text-color: var(--gatx-typography-color-text);
  --gatx-accordion-disabled-text-color: var(--gatx-colors-light-9);
  --gatx-accordion-icon-size: 1rem;

  --gatx-accordion-hover-bg-color: var(--gatx-colors-light-2);
  --gatx-accordion-focus-bg-color: var(--gatx-colors-light-2);

  --gatx-accordion-body-padding-x: 0;
  --gatx-accordion-body-padding-y: var(--gatx-spacing-sm);
  --gatx-accordion-button-padding: var(--gatx-spacing-md);

  @media (prefers-color-scheme: dark) {
    --gatx-accordion-disabled-text-color: var(--gatx-colors-dark-4);
    --gatx-accordion-outline-base-color: var(--gatx-colors-secondary-5);
    --gatx-accordion-border-color: var(--gatx-colors-dark-5);
    --gatx-accordion-hover-bg-color: var(--gatx-colors-dark-6);
    --gatx-accordion-focus-bg-color: var(--gatx-colors-dark-6);
  }
}

.accordion-card {
  --gatx-accordion-text-color: var(--gatx-colors-light-1);
  --gatx-accordion-disabled-text-color: var(--gatx-colors-dark-5);

  --gatx-accordion-bg-color: var(--gatx-colors-primary-9);
  --gatx-accordion-body-bg-color: var(--gatx-colors-light-1);
  --gatx-accordion-border-radius: var(--gatx-border-radius);
  --gatx-accordion-box-shadow: 0px 2px 4px 0px #00000013;
  --gatx-accordion-padding: var(--gatx-spacing-lg);
  --gatx-accordion-space-y: var(--gatx-spacing-md);

  --gatx-accordion-body-padding-x: var(--gatx-spacing-sm);
  --gatx-accordion-body-padding-y: var(--gatx-spacing-md);
  --gatx-accordion-button-padding: var(--gatx-spacing-md) var(--gatx-spacing-lg);

  --gatx-accordion-active-bg-color: var(--gatx-colors-primary-7);
  --gatx-accordion-hover-bg-color: var(--gatx-colors-primary-7);
  --gatx-accordion-focus-bg-color: var(--gatx-colors-primary-7);
  --gatx-accordion-disabled-bg-color: var(--gatx-colors-dark-2);

  @media (prefers-color-scheme: dark) {
    --gatx-accordion-text-color: var(--gatx-typography-color-text);
    --gatx-accordion-disabled-text-color: var(--gatx-colors-dark-4);

    --gatx-accordion-bg-color: var(--gatx-colors-primary-8);
    --gatx-accordion-body-bg-color: var(--gatx-colors-dark-6);
    --gatx-accordion-active-bg-color: var(--gatx-colors-primary-5);
    --gatx-accordion-hover-bg-color: var(--gatx-colors-primary-5);
    --gatx-accordion-focus-bg-color: var(--gatx-colors-primary-5);
    --gatx-accordion-disabled-bg-color: var(--gatx-colors-dark-10);
  }
}

.accordion-indicators {
  display: inline-flex;
  width: 100%;
  margin: 0 var(--gatx-spacing-sm);
  gap: 0 var(--gatx-spacing-sm);
}

.accordion-actions {
  display: flex;
  justify-content: end;
  margin-bottom: var(--gatx-spacing-sm);
  gap: var(--gatx-spacing-xs);
}

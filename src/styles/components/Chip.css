/**
* Base Component Definition
*/

.chips {
  --gatx-chips-active-outline: unset;
  --gatx-chips-active-bg: unset;
  --gatx-chips-active-color: unset;
  --gatx-chips-hover-outline: unset;
  --gatx-chips-hover-bg: unset;
  --gatx-chips-active-hover-bg: unset;
  --gatx-chips-focus-outline: unset;
  --gatx-chips-focus-bg: unset;
  --gatx-chips-focus-box-shadow: unset;
  --gatx-chips-disabled-bg: unset;
  --gatx-chips-padding: unset;
  --gatx-chips-bg: unset;
  --gatx-chips-font-size: unset;
  --gatx-chips-color: unset;

  display: inline-block;
  padding: var(--gatx-chips-padding);
  border-radius: 3.2px;
  outline: var(--gatx-chips-outline);
  background-color: var(--gatx-chips-bg);
  color: var(--gatx-chips-color);
  font-weight: var(--gatx-typography-font-weight-regular);
  font-size: var(--gatx-chips-font-size);
  line-height: var(--gatx-typography-line-height);
  font-family: var(--gatx-typography-font-family);

  &:not(:has(input:disabled)) {
    &:active,
    &:has(input:active),
    &:has(input:checked) {
      outline: var(--gatx-chips-active-outline);
      background-color: var(--gatx-chips-active-bg);
      color: var(--gatx-chips-active-color);
    }

    &:hover,
    &:has(input:hover) {
      outline: var(--gatx-chips-hover-outline);
      background-color: var(--gatx-chips-hover-bg);
      color: var(--gatx-chips-hover-color);
      cursor: pointer;
    }

    &:focus-visible,
    &:has(input:focus-visible) {
      outline: var(--gatx-chips-focus-outline);
      background-color: var(--gatx-chips-focus-bg);
      box-shadow: var(--gatx-chips-focus-box-shadow);
      color: var(--gatx-chips-focus-color);
    }

    &:active:hover,
    &:has(input:active:hover),
    &:has(input:checked:hover) {
      outline: var(--gatx-chips-active-outline);
      background-color: var(--gatx-chips-active-hover-bg);
      color: var(--gatx-chips-active-color);
    }
  }

  &:has(input:disabled) {
    background-color: var(--gatx-chips-disabled-bg);
    color: var(--gatx-chips-disabled-color);
    cursor: not-allowed;
  }
}

/**
* Variants - Style
*/

.chips-sm {
  --gatx-chips-padding: 3px 8px;
  --gatx-chips-bg: var(--gatx-colors-light-6);
  --gatx-chips-font-size: var(--gatx-typography-font-size-xs);
  --gatx-chips-color: var(--gatx-colors-dark-5);

  --gatx-chips-active-bg: var(--gatx-colors-light-10);
  --gatx-chips-active-color: var(--gatx-colors-light-1);

  --gatx-chips-hover-bg: var(--gatx-colors-light-10);
  --gatx-chips-hover-color: var(--gatx-colors-light-1);

  --gatx-chips-active-hover-bg: var(--gatx-colors-light-8);

  --gatx-chips-focus-bg: var(--gatx-colors-light-10);
  --gatx-chips-focus-color: var(--gatx-colors-light-1);
  --gatx-chips-focus-box-shadow: 0px 0px 0px 4px
    color-mix(in srgb, var(--gatx-colors-primary-7), transparent 40%);

  --gatx-chips-disabled-bg: var(--gatx-colors-light-6);
  --gatx-chips-disabled-color: var(--gatx-colors-light-8);

  @media (prefers-color-scheme: dark) {
    --gatx-chips-bg: var(--gatx-colors-dark-8);
    --gatx-chips-color: var(--gatx-colors-dark-3);

    --gatx-chips-active-bg: var(--gatx-colors-dark-10);
    --gatx-chips-active-color: var(--gatx-colors-light-1);

    --gatx-chips-hover-bg: var(--gatx-colors-dark-10);
    --gatx-chips-hover-color: var(--gatx-colors-light-1);

    --gatx-chips-active-hover-bg: var(--gatx-colors-dark-9);

    --gatx-chips-focus-bg: var(--gatx-colors-dark-10);
    --gatx-chips-focus-color: var(--gatx-colors-light-1);
    --gatx-chips-focus-box-shadow: 0px 0px 0px 4px
      color-mix(in srgb, var(--gatx-colors-secondary-5), transparent 40%);

    --gatx-chips-disabled-bg: var(--gatx-colors-dark-8);
    --gatx-chips-disabled-color: var(--gatx-colors-dark-5);
  }
}

.chips-lg {
  --gatx-chips-padding: 8px 16px;
  --gatx-chips-bg: var(--gatx-colors-light-5);
  --gatx-chips-font-size: var(--gatx-typography-font-size-regular);
  --gatx-chips-color: var(--gatx-colors-dark-5);

  --gatx-chips-active-bg: var(--gatx-colors-primary-1);
  --gatx-chips-active-outline: 1px solid var(--gatx-colors-primary-7);
  --gatx-chips-active-color: var(--gatx-colors-dark-8);

  --gatx-chips-hover-bg: var(--gatx-colors-primary-1);
  --gatx-chips-hover-outline: 1px solid var(--gatx-colors-primary-7);
  --gatx-chips-hover-color: var(--gatx-colors-dark-8);

  --gatx-chips-active-hover-bg: var(--gatx-colors-primary-2);

  --gatx-chips-focus-bg: var(--gatx-colors-primary-1);
  --gatx-chips-focus-outline: 1px solid var(--gatx-colors-primary-7);
  --gatx-chips-focus-color: var(--gatx-colors-dark-8);
  --gatx-chips-focus-box-shadow: 0px 0px 0px 4px
    color-mix(in srgb, var(--gatx-colors-primary-7), transparent 40%);

  --gatx-chips-disabled-bg: var(--gatx-colors-light-5);
  --gatx-chips-disabled-color: var(--gatx-colors-light-8);

  @media (prefers-color-scheme: dark) {
    --gatx-chips-bg: var(--gatx-colors-dark-8);
    --gatx-chips-color: var(--gatx-colors-dark-3);

    --gatx-chips-active-bg: color-mix(
      in srgb,
      var(--gatx-colors-secondary-9),
      transparent 30%
    );

    --gatx-chips-active-outline: 1px solid var(--gatx-colors-secondary-5);
    --gatx-chips-active-color: var(--gatx-colors-light-1);

    --gatx-chips-hover-bg: color-mix(
      in srgb,
      var(--gatx-colors-secondary-9),
      transparent 30%
    );
    --gatx-chips-hover-outline: 1px solid var(--gatx-colors-secondary-5);
    --gatx-chips-hover-color: var(--gatx-colors-light-1);

    --gatx-chips-active-hover-bg: var(--gatx-colors-secondary-9);

    --gatx-chips-focus-bg: color-mix(
      in srgb,
      var(--gatx-colors-secondary-9),
      transparent 30%
    );
    --gatx-chips-focus-outline: 1px solid var(--gatx-colors-secondary-5);
    --gatx-chips-focus-color: var(--gatx-colors-light-1);
    --gatx-chips-focus-box-shadow: 0px 0px 0px 4px
      color-mix(in srgb, var(--gatx-colors-secondary-5), transparent 40%);

    --gatx-chips-disabled-bg: var(--gatx-colors-dark-8);
    --gatx-chips-disabled-color: var(--gatx-colors-dark-4);
  }
}

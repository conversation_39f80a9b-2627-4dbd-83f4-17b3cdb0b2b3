import React, { ElementType, useId } from 'react'
import { ChevronDown, ChevronUp } from 'react-bootstrap-icons'
import Collapse from 'react-bootstrap/Collapse'
import { ResourceButton } from './ResourceButton'

type Props = {
  children?: React.ReactNode
  title: string
  Icon: ElementType
  isOpen: boolean
  onToggle: () => void
  onToggleEnd: () => void
}

const ResourceSection = ({
  children,
  title,
  isOpen,
  Icon,
  onToggle,
  onToggleEnd,
}: Props) => {
  const id = useId()
  const collapseId = useId()

  const Chevron = isOpen ? ChevronUp : ChevronDown

  return (
    <div className="flex flex-col">
      <ResourceButton
        id={id}
        text={
          <span
            className="flex justify-between items-center whitespace-nowrap"
            aria-label={`Open ${title}`}
          >
            {title} <Chevron aria-hidden className="resource-sidebar-icon" />
          </span>
        }
        onClick={onToggle}
        aria-controls={collapseId}
        Icon={Icon}
        aria-expanded={isOpen}
      />

      <Collapse in={isOpen} onExited={onToggleEnd}>
        <ul id={collapseId} aria-labelledby={id}>
          {children}
        </ul>
      </Collapse>
    </div>
  )
}

export { ResourceSection }

import '@/styles/index.css'
import '@/styles/storybook.css'
import '@/styles/tailwind.css'
import type { Preview } from '@storybook/react'
import { themes } from '@storybook/theming'

const preview: Preview = {
  tags: ['autodocs'],
  parameters: {
    backgrounds: { disable: true },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    darkMode: {
      classTarget: 'html',
      stylePreview: true,
      dark: { ...themes.dark },
      light: { ...themes.normal },
    },
    docs: {
      toc: true,
    },
  },
  argTypes: {
    children: {
      table: { category: 'Children', disable: true },
    },
  },
}

export default preview

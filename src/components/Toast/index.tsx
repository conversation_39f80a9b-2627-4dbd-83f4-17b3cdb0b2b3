import clsx from 'clsx'
import {
  Toast as BootstrapToast,
  ToastContainer as <PERSON>trapToastContainer,
  ToastProps as BootstrapToastProps,
} from 'react-bootstrap'
import * as Icons from 'react-bootstrap-icons'
import { Spinner } from '../Spinner'

const AUTO_CLOSE_SECONDS = 5000

// Icon names available for use in toasts
export type IconName = keyof typeof Icons

type Props = BootstrapToastProps & {
  /** The toast's title. */
  title: string
  /** The toast's text. */
  text?: string
  /** The specific style of toast to render */
  variant: 'info' | 'success' | 'error'
  /** The icon to render within the button, if any.
   * Access https://icons.getbootstrap.com/ to see the list of icons.
   * The icon name should be passed as a string, using PascalCase convention e.g. 'PlusSquare' or 'ArrowLeftShort'.
   * Pass 'Spinner' to render a spinner component.
   */
  icon?: IconName | 'Spinner'
}

const Toast = ({ text, variant, icon, title, ...props }: Props) => {
  // Defines the icon component to render, if any
  const IconComponent = icon && icon !== 'Spinner' ? Icons[icon] : null
  return (
    <BootstrapToastContainer position="top-end">
      <BootstrapToast
        className={clsx(`toast-${variant}`)}
        autohide
        delay={AUTO_CLOSE_SECONDS}
        {...props}
      >
        <BootstrapToast.Header closeButton={false}>
          {title}
        </BootstrapToast.Header>
        {text && <BootstrapToast.Body>{text}</BootstrapToast.Body>}
        {IconComponent && (
          <IconComponent className="icon" role="presentation" />
        )}
        {icon === 'Spinner' && <Spinner ariaHidden className="icon" />}
      </BootstrapToast>
    </BootstrapToastContainer>
  )
}

export { ToastProvider, useShowToast } from './ToastProvider'
export { Toast }

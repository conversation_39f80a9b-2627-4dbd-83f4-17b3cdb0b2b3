import { render } from '@/tests/react'
import { axe } from 'jest-axe'
import { HTMLProps } from 'react'
import { FormField } from '.'

describe('FormField', () => {
  const Input = (props: HTMLProps<HTMLInputElement>) => (
    <input type="text" {...props} onChange={() => undefined} />
  )

  describe('FormField', () => {
    test('renders the given input with the provided value', () => {
      const component = render(
        <FormField
          label="Label"
          description="Description"
          error="Error Message"
        >
          <Input value="value" />
        </FormField>,
      )

      expect(component.getByLabelText('Label')).toHaveValue('value')
      expect(component.getByText('Description')).toBeInTheDocument()
      expect(component.getByText(/Error Message/i)).toBeInTheDocument()
    })

    test('renders the input with the given id', () => {
      const component = render(
        <FormField
          label="Label"
          description="Description"
          error="Error Message"
        >
          <Input value="value" id="custom-id" />
        </FormField>,
      )

      expect(component.getByLabelText('Label')).toHaveAttribute(
        'id',
        'custom-id',
      )
    })

    test('renders the input in read only state', () => {
      const component = render(
        <FormField
          label="Label"
          description="Description"
          error="Error Message"
          readOnly
        >
          <Input value="value" />
        </FormField>,
      )

      expect(component.getByLabelText('Label')).toHaveAttribute('readOnly')
      expect(component.getByLabelText('Label')).toHaveValue('value')
    })

    test.each([{ readOnly: true }, { readOnly: false }])(
      'adheres to accessibility guidelines with readOnly=$readOnly',
      async ({ readOnly }) => {
        const { container } = render(
          <FormField
            label="Label"
            description="Description"
            error="Error Message"
            readOnly={readOnly}
          >
            <Input value="value" />
          </FormField>,
        )

        expect(await axe(container)).toHaveNoViolations()
      },
    )
  })
})

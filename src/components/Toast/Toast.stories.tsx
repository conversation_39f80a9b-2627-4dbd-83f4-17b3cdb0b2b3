import { Meta, StoryObj } from '@storybook/react'
import { Toast } from '.'

const meta: Meta<typeof Toast> = {
  title: 'Components/Toast',
  component: Toast,
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Toast component for displaying messages to the user, with optional icon.',
      },
    },
  },
  args: {
    title: 'Title',
    variant: 'info',
  },
}

export const Info: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Toast component for displaying info messages to the user, with optional Spinner icon.',
      },
    },
  },
  args: {
    title: 'Saving',
    icon: 'Spinner',
    variant: 'info',
  },
}

export const Error: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Toast component for displaying error messages to the user, with optional icon.',
      },
    },
  },
  args: {
    title: 'Error',
    icon: 'ExclamationTriangleFill',
    text: 'Please Retry',
    variant: 'error',
  },
}
export const Success: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Toast component for displaying success messages to the user, with optional icon.',
      },
    },
  },
  args: {
    title: 'Success',
    icon: 'CheckCircleFill',
    variant: 'success',
  },
}

import { render, screen } from '@/tests/react'
import { axe } from 'jest-axe'
import { Link } from '.'

describe('Links', () => {
  test.each(['underline', 'outline'] as const)(
    '%s variant adheres to accessibility guidelines',
    async (variant: 'underline' | 'outline') => {
      const { container } = render(
        <Link variant={variant} href="https://www.gatx.com/" aria-label="Link">
          Link
        </Link>,
      )

      expect(await axe(container)).toHaveNoViolations()
    },
  )

  test('formats aria-label when link opens in new tab', () => {
    render(
      <Link
        variant="underline"
        href="https://www.gatx.com/"
        aria-label="Link"
        newTab
      >
        Link
      </Link>,
    )

    expect(screen.getByText('Link')).toHaveAttribute(
      'aria-label',
      'Link (opens in a new tab)',
    )
  })
})

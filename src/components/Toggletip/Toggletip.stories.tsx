import { withActions } from '@storybook/addon-actions/decorator'
import type { Meta, StoryObj } from '@storybook/react'
import { Toggletip } from './index'

const meta: Meta<typeof Toggletip> = {
  title: 'Components/Toggletip',
  component: Toggletip,
  parameters: {
    layout: 'padded',
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    text: 'Tooltip text',
    label: 'Tooltip text',
    disabled: false,
    onClick: () => console.log('Toggletip clicked'),
  },
  parameters: {
    layout: 'centered',
    actions: { handles: ['click'] },
  },
  decorators: [withActions],
}

.text-input {
  --gatx-form-field-text-input-bg: var(--gatx-colors-light-5);
  --gatx-form-field-text-input-color: var(--gatx-colors-dark-5);
  --gatx-form-field-text-input-outline-color: var(--gatx-colors-primary-7);

  @media (prefers-color-scheme: dark) {
    --gatx-form-field-text-input-bg: var(--gatx-colors-dark-8);
    --gatx-form-field-text-input-color: var(--gatx-colors-dark-2);
  }

  height: 40px;
  padding-right: var(--gatx-spacing-md);
  padding-left: var(--gatx-spacing-md);
  border-radius: var(--gatx-border-radius);
  background: var(--gatx-form-field-text-input-bg);
  color: var(--gatx-form-field-text-input-color);

  &:active,
  &:focus-visible {
    outline: 1px solid var(--gatx-form-field-text-input-outline-color);
    --gatx-form-field-text-input-color: var(--gatx-colors-dark-8);

    @media (prefers-color-scheme: dark) {
      --gatx-form-field-text-input-color: var(--gatx-colors-light-1);
      --gatx-form-field-text-input-outline-color: var(
        --gatx-colors-secondary-5
      );
    }
  }

  &:disabled {
    --gatx-form-field-text-input-color: var(--gatx-colors-light-8);

    @media (prefers-color-scheme: dark) {
      --gatx-form-field-text-input-color: var(--gatx-colors-dark-4);
    }
  }
}

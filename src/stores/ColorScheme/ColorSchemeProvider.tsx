import { PropsWithChildren, useLayoutEffect } from 'react'
import { useColorScheme } from './useColorScheme'

/**
 * ColorSchemeProvider is extremely basic, and just does one thing -- it fetches
 * the user's color scheme selection once it's available, and sets the
 * appropriate CSS class on the document element.
 *
 * Next.js's RootLayout element _has_ to include the `html` and `body` elements,
 * so we can't render them here, and instead have to rely on `useLayoutEffect`
 * to apply the appropriate class on initial page render. Because we're
 * dependent on an asynchronous fetch to get session data for the user, this can
 * cause a minor flash-of-unstyled-content (FOUC) in spite of the LayoutEffect,
 * particularly in development environments where initial page load frequently
 * requires a multi-second recompile.
 */
export const ColorSchemeProvider = ({ children }: PropsWithChildren) => {
  const [colorScheme] = useColorScheme()

  useLayoutEffect(() => {
    document.documentElement.className = colorScheme ?? ''
  }, [colorScheme])

  return children
}

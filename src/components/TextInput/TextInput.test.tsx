import { render } from '@/tests/react'
import { axe } from 'jest-axe'
import { TextInput } from '.'
import { FormField } from '../FormField'

describe('TextInput', () => {
  test('adheres to accessibility guidelines', async () => {
    const { container } = render(
      <FormField label="TextInput">
        <TextInput value="test" onChange={() => console.log()} />
      </FormField>,
    )
    expect(await axe(container)).toHaveNoViolations()
  })
})

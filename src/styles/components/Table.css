/**
* Base Component Definition
*/
.table {
  --gatx-table-header-color: var(--gatx-colors-dark-8);
  --gatx-table-body-color: var(--gatx-colors-dark-5);
  --gatx-table-bg-color: var(--gatx-colors-light-4);
  --gatx-table-striped-bg-color: var(--gatx-colors-light-7);

  --gatx-table-heading-padding: 8px 8px 9px 16px;
  --gatx-table-data-padding: 14px 8px 14px 16px;

  --gatx-table-drop-shadow: 5px 0 5px rgba(0, 0, 1, 0.09);

  @media (prefers-color-scheme: dark) {
    --gatx-table-header-color: var(--gatx-colors-light-1);
    --gatx-table-body-color: var(--gatx-colors-dark-2);
    --gatx-table-striped-bg-color: var(--gatx-colors-dark-8);
    --gatx-table-bg-color: var(--gatx-colors-dark-7);
    --gatx-table-drop-shadow: 5px 0 5px rgba(0, 0, 1, 0.3);
  }

  width: 100%;
  margin-bottom: 0;
  color: var(--gatx-table-body-color);
  font-size: var(--gatx-typography-font-size-sm);
  vertical-align: top;

  thead > tr > th {
    vertical-align: middle;
  }

  td {
    padding: var(--gatx-table-data-padding);
  }

  th {
    padding: var(--gatx-table-heading-padding);
    color: var(--gatx-table-header-color);
    font-weight: 400;
    text-align: left;
  }

  > tbody {
    vertical-align: top;

    th {
      padding: var(--gatx-table-heading-padding);
    }
  }

  &.sticky {
    tr {
      > th {
        position: sticky;
        left: 0;
        background-color: var(--gatx-table-bg-color);

        &:first-child {
          z-index: 1;
        }

        &:first-child::after {
          z-index: 2;
          position: absolute;
          top: 0;
          right: 0;
          width: 3px;
          height: 100%;
          box-shadow: var(--gatx-table-drop-shadow);
          content: '';
        }
      }
    }
    thead > tr > th {
      z-index: 1;
      position: sticky;
      top: 0;
      background-color: var(--gatx-table-bg-color);

      &:first-child {
        z-index: 2;
      }

      &:first-child::after {
        z-index: 2;
        position: absolute;
        top: 0;
        right: 0;
        width: 3px;
        height: 100%;
        box-shadow: var(--gatx-table-drop-shadow);
        content: '';
      }
    }
  }

  > thead {
    vertical-align: bottom;

    th {
      padding: var(--gatx-table-heading-padding);
    }
  }

  td.no-vertical-padding {
    padding-top: 0;
    padding-bottom: 0;
  }
}

.table-striped {
  > tbody > tr:not(.collapse-row):nth-of-type(odd) {
    background-color: var(--gatx-table-striped-bg-color);
    > th {
      background-color: var(--gatx-table-striped-bg-color);
    }
  }
}

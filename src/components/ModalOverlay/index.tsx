import { useI18n } from '@/i18n/useI18n'
import clsx from 'clsx'
import {
  cloneElement,
  FC,
  HTMLAttributes,
  ReactElement,
  ReactNode,
  useEffect,
} from 'react'
import { Modal as BootstrapModal, Spinner } from 'react-bootstrap'
import { CloseButton } from '../CloseButton'

/**
 * These props are passed to all footers - in addition to their custom props.
 */
type FooterProps = HTMLAttributes<HTMLDivElement> & {
  onHide?: () => void
}

const SIZE_MAPPINGS = {
  small: 'sm',
  medium: undefined,
  large: 'lg',
  fullscreen: 'full',
} as const
type Size = keyof typeof SIZE_MAPPINGS

type Props = HTMLAttributes<HTMLDivElement> & {
  /** What is displayed in the content of the modal */
  children: ReactNode
  /** Callback to handle when modal is shown */
  onShow?: () => void
  /** Callback to handle when modal is hidden */
  onHide?: () => void
  /** Whether or not to show the modal */
  show: boolean
  /** The specific style of dialog to render. See below for supported styles and their intended uses. */
  size?: Size
  /** Allow the user to click anywhere outside the dialog box to dismiss it (defaults to true) */
  clickAnywhereToDismiss?: boolean
  /** True to include the "X" close button in the top/right (defaults to true) */
  hasCloseButton?: boolean
  /** The title of the modal */
  title: string
  footer?: ReactElement<FooterProps>
  headerComponent?: ReactNode
  /** Whether or not the modal is in a loading state */
  showSpinner?: boolean
}

const ModalHeaderWrapper = ({
  headerComponent,
  title,
  hasCloseButton,
  onHide = () => {
    /** no op */
  },
}: Pick<Props, 'headerComponent' | 'title' | 'hasCloseButton' | 'onHide'>) => {
  return (
    <div className="flex items-center justify-between w-full">
      <h2 className={`modal-title ${headerComponent ? 'sr-only' : ''}`}>
        {title}
      </h2>
      {headerComponent && headerComponent}

      {hasCloseButton && (
        <CloseButton
          className="btn-close"
          type="button"
          aria-label="Close"
          onClick={onHide}
        />
      )}
    </div>
  )
}

const ModalSpinner = () => {
  const { t } = useI18n()
  return (
    <>
      <div className="modal-backdrop" />
      <div className="modal-spinner">
        <Spinner className="spinner" />

        <h1 tabIndex={0} className="sr-only">
          {t('loading')}
        </h1>
      </div>
    </>
  )
}

const ModalOverlay: FC<Props> = ({
  show,
  onShow,
  onHide = () => {
    /** no op */
  },
  size = 'medium',
  clickAnywhereToDismiss = true,
  hasCloseButton = true,
  children,
  footer,
  title,
  headerComponent,
  showSpinner,
  ...props
}: Props) => {
  clickAnywhereToDismiss = showSpinner ? false : clickAnywhereToDismiss

  useEffect(() => {
    if (showSpinner) {
      const handleKeyPress = (event: KeyboardEvent) => {
        event.preventDefault()
      }
      window.addEventListener('keydown', handleKeyPress)

      return () => {
        window.removeEventListener('keydown', handleKeyPress)
      }
    }
  }, [showSpinner])

  return (
    <div>
      {showSpinner ? (
        <ModalSpinner />
      ) : (
        <BootstrapModal
          show={show}
          onShow={onShow}
          onHide={onHide}
          centered={true}
          backdrop={clickAnywhereToDismiss ? true : 'static'}
          className={clsx(
            size !== 'fullscreen' ? `modal-${SIZE_MAPPINGS[size]}` : '',
            props.className,
          )}
          fullscreen={size === 'fullscreen' ? true : ''}
        >
          <BootstrapModal.Header>
            <ModalHeaderWrapper
              title={title}
              onHide={onHide}
              hasCloseButton={hasCloseButton}
              headerComponent={headerComponent}
            />
          </BootstrapModal.Header>
          <BootstrapModal.Body>{children}</BootstrapModal.Body>
          {footer && (
            <BootstrapModal.Footer>
              {cloneElement(footer, { onHide: onHide })}
            </BootstrapModal.Footer>
          )}
        </BootstrapModal>
      )}
    </div>
  )
}

export { ModalOverlay, type FooterProps }

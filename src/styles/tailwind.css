@import 'tailwindcss';
@source not inline("collapse");
@source "../node_modules/@gatx-corp/platform-one-common";

@theme {
  --color-*: initial;

  --color-primary: #005695; /* Primary 7 */
  --color-primary-dark: #003962; /* Primary 9 */

  --color-primary-1: #e5f1f9;
  --color-primary-2: #acd1ed;
  --color-primary-3: #73b2e1;
  --color-primary-4: #3992d4;
  --color-primary-5: #0073c8;
  --color-primary-6: #0065af;
  --color-primary-7: #005695;
  --color-primary-8: #00477c;
  --color-primary-9: #003962;
  --color-primary-10: #002a49;

  --color-secondary-1: #edf7ff;
  --color-secondary-2: #c3e6ff;
  --color-secondary-3: #9ad5ff;
  --color-secondary-4: #71c4ff;
  --color-secondary-5: #47b3ff;
  --color-secondary-6: #0093fc;
  --color-secondary-7: #077acc;
  --color-secondary-8: #0e619c;
  --color-secondary-9: #15486c;
  --color-secondary-10: #294252;

  --color-tertiary-1: #00e2cf;
  --color-tertiary-2: #00c8b8;
  --color-tertiary-3: #00afa0;
  --color-tertiary-4: #00a293;
  --color-tertiary-5: #009589;
  --color-tertiary-6: #00867c;
  --color-tertiary-7: #007c72;
  --color-tertiary-8: #00625a;
  --color-tertiary-9: #004943;
  --color-tertiary-10: #00332f;

  --color-light-1: #fdfdfd;
  --color-light-2: #f8f9f9;
  --color-light-3: #f5f6f6;
  --color-light-4: #f1f2f2;
  --color-light-5: #eeeff0;
  --color-light-6: #eaebec;
  --color-light-7: #d5d6d7;
  --color-light-8: #a6a7a8;
  --color-light-9: #818182;
  --color-light-10: #626363;

  --color-dark-1: #eaebec;
  --color-dark-2: #bdc2c5;
  --color-dark-3: #9da4a9;
  --color-dark-4: #707b82;
  --color-dark-5: #55616a;
  --color-dark-6: #2a3a45;
  --color-dark-7: #26353f;
  --color-dark-8: #1e2931;
  --color-dark-9: #172026;
  --color-dark-10: #12181d;

  --color-error-1: #fae6e6;
  --color-error-2: #f3b3b2;
  --color-error-3: #ed807e;
  --color-error-4: #e74d4a;
  --color-error-5: #e01a16;
  --color-error-6: #b21a18;
  --color-error-7: #831919;
  --color-error-8: #55191b;
  --color-error-9: #27181c;
  --color-error-10: #160e10;

  --color-warning-1: #fdf5e5;
  --color-warning-2: #fde3af;
  --color-warning-3: #fcd27a;
  --color-warning-4: #fbc144;
  --color-warning-5: #fbaf0e;
  --color-warning-6: #c78d12;
  --color-warning-7: #926b15;
  --color-warning-8: #5e4919;
  --color-warning-9: #29271c;
  --color-warning-10: #1e1d15;

  --color-success-1: #e4f6f0;
  --color-success-2: #abe7d2;
  --color-success-3: #72d7b3;
  --color-success-4: #39c895;
  --color-success-5: #00b977;
  --color-success-6: #049563;
  --color-success-7: #08714f;
  --color-success-8: #0c4c3a;
  --color-success-9: #102826;
  --color-success-10: #0a1a18;

  --spacing-*: initial;

  --spacing-0: 0px;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;

  --text-*: initial;
  --text-title-xl: 3rem;
  --text-title-xl--font-weight: var(--gatx-typography-font-weight-medium);
  --text-title-xl--line-height: var(--gatx-typography-line-height-title);
  --text-title-lg: 2.5rem;
  --text-title-lg--font-weight: var(--gatx-typography-font-weight-medium);
  --text-title-lg--line-height: var(--gatx-typography-line-height-title);
  --text-title-md: 2rem;
  --text-title-md--font-weight: var(--gatx-typography-font-weight-medium);
  --text-title-md--line-height: var(--gatx-typography-line-height-title);
  --text-title-rg: 1.5rem;
  --text-title-rg--font-weight: var(--gatx-typography-font-weight-medium);
  --text-title-rg--line-height: var(--gatx-typography-line-height-title);
  --text-title-sm: 1.25rem;
  --text-title-sm--font-weight: var(--gatx-typography-font-weight-medium);
  --text-title-sm--line-height: var(--gatx-typography-line-height-title);
  --text-title-xs: 1.125rem;
  --text-title-xs--font-weight: var(--gatx-typography-font-weight-medium);
  --text-title-xs--line-height: var(--gatx-typography-line-height-title);

  --text-body-ld: 1.25rem;
  --text-body-ld--font-weight: var(--gatx-typography-font-weight-regular);
  --text-body-ld--line-height: var(--gatx-typography-line-height);
  --text-body-sm-ld: 1.125rem;
  --text-body-sm-ld--font-weight: var(--gatx-typography-font-weight-regular);
  --text-body-sm-ld--line-height: var(--gatx-typography-line-height);
  --text-body-rg: 1rem;
  --text-body-rg--font-weight: var(--gatx-typography-font-weight-regular);
  --text-body-rg--line-height: var(--gatx-typography-line-height);
  --text-body-sm: 0.875rem;
  --text-body-sm--font-weight: var(--gatx-typography-font-weight-regular);
  --text-body-sm--line-height: var(--gatx-typography-line-height);
  --text-body-xs: 0.75rem;
  --text-body-xs--font-weight: var(--gatx-typography-font-weight-regular);
  --text-body-xs--line-height: var(--gatx-typography-line-height);
}

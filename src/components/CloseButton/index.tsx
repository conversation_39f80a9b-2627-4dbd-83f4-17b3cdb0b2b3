import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react'
import {
  CloseButton as <PERSON>trap<PERSON>lose<PERSON><PERSON><PERSON>,
  ButtonProps,
} from 'react-bootstrap'
import { X } from 'react-bootstrap-icons'

type Props = ButtonProps & {
  /**  The click handler for the button */
  onClick: MouseEventHandler<HTMLButtonElement>
  /**  The disabled state of the button */
  disabled?: boolean
}

const CloseButton = (props: Props) => {
  return (
    <BootstrapCloseButton aria-label="Close" {...props}>
      <X role="presentation" className="icon" />
    </BootstrapCloseButton>
  )
}

export { CloseButton }

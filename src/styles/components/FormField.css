.form-field {
  --gatx-form-field-error-color: var(--gatx-colors-error-5);
  --gatx-form-field-warning-color: var(--gatx-colors-warning-5);
  --gatx-form-field-description-color: var(--gatx-colors-dark-5);
  --gatx-form-field-disabled-color: var(--gatx-colors-light-8);

  @media (prefers-color-scheme: dark) {
    --gatx-form-field-error-color: var(--gatx-colors-error-2);
    --gatx-form-field-warning-color: var(--gatx-colors-warning-2);
    --gatx-form-field-description-color: var(--gatx-colors-dark-2);
    --gatx-form-field-disabled-color: var(--gatx-colors-dark-4);
  }

  .input-label {
    display: block;
    margin-bottom: var(--gatx-spacing-sm);
    color: var(--gatx-typography-color-text);
  }

  .input-description {
    margin-top: var(--gatx-spacing-xs);
    color: var(--gatx-form-field-description-color);
    font-size: var(--gatx-typography-font-size-xs);
  }

  .input-error,
  .input-warning {
    display: flex;
    max-width: 250px;
    margin-top: 8px;
    gap: var(--gatx-spacing-xs);
    color: var(--gatx-form-field-error-color);
    font-size: var(--gatx-typography-font-size-xs);
  }

  .input-error {
    color: var(--gatx-form-field-error-color);
  }

  .input-warning {
    color: var(--gatx-form-field-warning-color);
  }

  .input {
    width: fit-content;
  }

  .input-required {
    display: flex;
    align-items: center;
    margin-top: var(--gatx-spacing-xs);
    gap: var(--gatx-spacing-xs);
    color: var(--gatx-colors-warning-4);
    font-size: var(--gatx-typography-font-size-xs);

    .required-icon {
      width: 0.8rem;
    }
  }

  &:has(input:disabled) {
    .input-label {
      color: var(--gatx-form-field-disabled-color);
    }
    .input-description {
      color: var(--gatx-form-field-disabled-color);
    }
  }

  &[data-readonly='true'] {
    .input-label {
      margin-bottom: 0;
    }

    .input-description {
      margin-top: 0;
      margin-bottom: var(--gatx-spacing-xs);
    }
  }

  &[data-required='true'] {
    &:not(&[data-fieldset='true']) {
      &:not(&[data-readonly='true']) {
        .input:not(:has(.wijmo-select)) {
          overflow: hidden;
          border-radius: var(--gatx-border-radius);
          outline: 1px solid var(--gatx-colors-warning-4);
        }

        .required-select {
          border-radius: var(--gatx-border-radius);
          outline: 1px solid var(--gatx-colors-warning-4);
        }
      }
    }
  }
}

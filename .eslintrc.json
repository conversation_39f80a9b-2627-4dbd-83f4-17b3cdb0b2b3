{
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": "latest",
    "sourceType": "module",
    "project": ["./tsconfig.json"],
    "extraFileExtensions": [".mdx", ".md", ".d.ts"]
  },
  "extends": [
    "eslint:recommended",
    "plugin:react/recommended",
    "plugin:react/jsx-runtime",
    "plugin:react-hooks/recommended-legacy",
    "plugin:@typescript-eslint/recommended-type-checked",
    "plugin:@typescript-eslint/stylistic-type-checked",
    "plugin:storybook/recommended",
    "plugin:prettier/recommended"
  ],
  "root": true,
  "env": { "browser": true },
  "ignorePatterns": ["dist", "storybook-static"],
  "rules": {
    "semi": ["error", "never"],
    "@typescript-eslint/consistent-type-definitions": ["error", "type"],
    "@typescript-eslint/interface-name-prefix": "off",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/no-unused-vars": [
      "error",
      { "argsIgnorePattern": "^_" }
    ],
    /**
     * Mixing default and named exports results in a confusing API surface,
     * where consumers must access default exports as "chunk.default" instead of the usual syntax.
     *
     * Thus, we restrict the usage of default exports in the codebase, except for config files and stories.
     */
    "no-restricted-exports": [
      "error",
      { "restrictDefaultExports": { "direct": true, "named": true } }
    ],
    "no-void": "error",
    "react-hooks/exhaustive-deps": "error",
    "react/no-unknown-property": "off"
  },
  "overrides": [
    {
      "files": ["*.config.ts", "*.stories.tsx"],
      "rules": {
        "no-restricted-exports": "off"
      }
    }
  ],
  "settings": {
    "react": {
      "version": "detect"
    }
  }
}

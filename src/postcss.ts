/* eslint-disable no-restricted-exports */
import type { PluginCreator } from 'postcss'
import postcssDarkThemeClass from 'postcss-dark-theme-class'
import postcssNesting from 'postcss-nesting'
import {
  DARK_THEME_CLASS,
  LIGHT_THEME_CLASS,
} from './stores/ColorScheme/constants'

const DARK_MODE_CONFIG = {
  darkSelector: `.${DARK_THEME_CLASS}`,
  lightSelector: `.${LIGHT_THEME_CLASS}`,
}

const postcssGATXPlatformOneCommon: PluginCreator<never> = () => {
  return {
    postcssPlugin: 'postcss-gatx-platform-one-common',
    plugins: [postcssNesting(), postcssDarkThemeClass(DARK_MODE_CONFIG)],
  }
}

postcssGATXPlatformOneCommon.postcss = true

export default postcssGATXPlatformOneCommon

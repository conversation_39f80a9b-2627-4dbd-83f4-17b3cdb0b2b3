import clsx from 'clsx'
import { ChangeEvent<PERSON>andler, HTMLProps } from 'react'
import { Check, RecordCircleFill } from 'react-bootstrap-icons'

const SIZE_MAPPINGS = { small: 'sm', large: 'lg' } as const

export type Size = keyof typeof SIZE_MAPPINGS

type Props = Omit<
  HTMLProps<HTMLInputElement>,
  'size' | 'disabled' | 'className'
> & {
  /** The type of control. Use `radio` for single select chips, and `checkbox` for multiselect chips. */
  type: 'checkbox' | 'radio'
  /** The size of the ChoiceControl when using the `chip` variant. */
  size?: Size
  /** The text label displayed inside the control. */
  label: string
  /** The value associated with this control. Used when the control is selected. */
  value: string
  /** The unique identifier for the control's group. This should be the same for all controls within a group. */
  name: string
  /** Whether the control is disabled. When true, the control cannot be interacted with. */
  disabled?: boolean
  /** Where the control is selected. When true, control is "checked". */
  isSelected?: boolean
  /** `classic`: standard checkbox / radio button | `chip`: chip version */
  variant?: 'classic' | 'chip'
  /** The function called when the control is selected or deselected. */
  onChange: ChangeEventHandler<HTMLInputElement>
}

/**
 * The ChoiceControl component represents a selectable option within a group.
 * It can be implemented as a `classic` or `chip` styled radio button or checkbox
 * allowing for single selection or multi selection
 * within a group of choices.
 *
 * This component is used within the ChoiceGroup component, and all of its props are passed from that component.
 */
export const ChoiceControl = ({
  variant = 'chip',
  size = 'large',
  disabled = false,
  label,
  value,
  type,
  name,
  isSelected,
  onChange,
  ...props
}: Props) => {
  const isClassic = variant === 'classic'

  return (
    <label
      className={clsx(
        isClassic
          ? `${type}-label flex items-start gap-sm`
          : `chips chips-${SIZE_MAPPINGS[size]}`,
      )}
    >
      <div className={clsx(isClassic ? `${type}-choice` : '')}>
        {isSelected && isClassic && type === 'checkbox' && <Check />}
        {isSelected && isClassic && type === 'radio' && <RecordCircleFill />}
        <input
          onChange={onChange}
          checked={isSelected}
          type={type}
          name={name}
          value={value}
          disabled={disabled}
          className="sr-only"
          {...props}
        />
      </div>
      {label}
    </label>
  )
}

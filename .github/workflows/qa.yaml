name: Quality Assurance

on: [push]

jobs:
  eslint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: oven-sh/setup-bun@v2.0.1
        with:
          bun-version-file: .tool-versions
      - run: bun install
      - run: bun lint

  tsc:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: oven-sh/setup-bun@v2.0.1
        with:
          bun-version-file: .tool-versions
      - run: bun install
      - run: bun tsc

  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: oven-sh/setup-bun@v2.0.1
        with:
          bun-version-file: .tool-versions
      - run: bun install
      - run: bun run build
      - run: bun run test

.filter-pill-group {
  display: flex;
  flex-wrap: wrap;
  gap: var(--gatx-spacing-sm);
}

.filter-pill {
  --gatx-filter-pill-border-color: var(--gatx-colors-primary-7);
  --gatx-filter-pill-text-color: var(--gatx-colors-dark-8);
  --gatx-filter-pill-bg-color: var(--gatx-colors-primary-1);
  --gatx-filter-pill-hover-bg-color: var(--gatx-colors-primary-2);
  --gatx-filter-pill-focus-bg-color: var(--gatx-colors-primary-2);
  --gatx-filter-pill-box-shadow-color: color-mix(
    in srgb,
    var(--gatx-colors-primary-7),
    transparent 60%
  );

  @media (prefers-color-scheme: dark) {
    --gatx-filter-pill-border-color: var(--gatx-colors-secondary-5);
    --gatx-filter-pill-text-color: var(--gatx-colors-light-1);
    --gatx-filter-pill-bg-color: color-mix(
      in srgb,
      var(--gatx-colors-secondary-9),
      transparent 70%
    );
    --gatx-filter-pill-hover-bg-color: var(--gatx-colors-secondary-9);
    --gatx-filter-pill-focus-bg-color: var(--gatx-colors-secondary-9);
    --gatx-filter-pill-box-shadow-color: color-mix(
      in srgb,
      var(--gatx-colors-secondary-5),
      transparent 60%
    );
  }

  display: flex;
  align-items: center;
  padding: var(--gatx-spacing-sm) var(--gatx-spacing-md);
  gap: var(--gatx-spacing-sm);
  border: 1px solid var(--gatx-filter-pill-border-color);
  border-radius: var(--gatx-border-radius);
  background-color: var(--gatx-filter-pill-bg-color);
  color: var(--gatx-filter-pill-text-color);

  font-weight: var(--gatx-typography-font-weight-regular);
  font-size: var(--gatx-typography-font-size-regular);
  line-height: var(--gatx-typography-line-height);

  transition:
    background-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;

  @media (prefers-reduced-motion: reduce) {
    transition: none;
  }

  .icon {
    width: var(--gatx-typography-font-size-sm);
    height: var(--gatx-typography-font-size-sm);
  }

  &:hover {
    background-color: var(--gatx-filter-pill-hover-bg-color);
  }

  &:focus-visible {
    outline: none;
    background-color: var(--gatx-filter-pill-hover-bg-color);
    box-shadow: 0px 0px 0px 4px var(--gatx-filter-pill-box-shadow-color);
  }
}

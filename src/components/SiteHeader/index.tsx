import { ElementType, PropsWithChildren } from 'react'
import { AccountSettingsDropDown } from './AccountSettingsDropDown'
import { Application } from './Application'

type Props = {
  application?: string
  Link?: ElementType
}

/**
 * The SiteHeader component defines a header used across different PlatformOne-wide
 * */
const SiteHeader = ({
  application,
  Link,
  children,
}: PropsWithChildren<Props>) => {
  return (
    <div className="w-full px-md h-[70px] flex justify-between items-center border-b border-light-7 dark:border-dark-6">
      <Application name={application} Link={Link} />
      {children}
      <AccountSettingsDropDown />
    </div>
  )
}

export { SiteHeader }

import { render, RenderResult, screen } from '@/tests/react'
import userEvent from '@testing-library/user-event'
import { axe } from 'jest-axe'
import { Accordion } from '.'

describe('Accordion', () => {
  let component: RenderResult

  beforeEach(() => {
    component = render(
      <Accordion.Container>
        <Accordion.Item title="First Accordion">
          <p>This is the first pane&apos;s content.</p>
        </Accordion.Item>
        <Accordion.Item title="Second Accordion">
          <p>This is the second pane&apos;s content.</p>
        </Accordion.Item>
      </Accordion.Container>,
    )
  })

  test('renders the content of the panels when opened', async () => {
    const button1 = screen.getByRole('button', { name: 'First Accordion' })
    const button2 = screen.getByRole('button', { name: 'Second Accordion' })

    await userEvent.click(button1)
    await userEvent.click(button2)

    const panel1 = screen.getByRole('region', { name: 'First Accordion' })
    const panel2 = screen.getByRole('region', { name: 'Second Accordion' })

    expect(panel1).toHaveTextContent("This is the first pane's content.")
    expect(panel2).toHaveTextContent("This is the second pane's content.")
  })

  test('toggles accordion panel when clicking its corresponding button', async () => {
    const button = screen.getByRole('button', { name: 'First Accordion' })

    function getPanel() {
      return screen.queryByRole('region', { name: 'First Accordion' })
    }

    // Opens the panel

    await userEvent.click(button)

    expect(button).toHaveAttribute('aria-expanded', 'true')
    expect(getPanel()).toBeVisible()

    // Closes the panel

    await userEvent.click(button)

    expect(button).toHaveAttribute('aria-expanded', 'false')
    expect(getPanel()).toBeFalsy()
  })

  test('toggles only one accordion panel by click', async () => {
    const button1 = screen.getByRole('button', { name: 'First Accordion' })
    const button2 = screen.getByRole('button', { name: 'Second Accordion' })

    function getPanel(name: string) {
      return screen.queryByRole('region', { name })
    }

    // The second panel remains closed when the first one is opened

    await userEvent.click(button1)
    expect(getPanel('First Accordion')).toBeVisible()
    expect(getPanel('Second Accordion')).toBeFalsy()

    // After opening the second panel, it remains open after the first one is closed

    await userEvent.click(button2)
    await userEvent.click(button1)

    expect(getPanel('First Accordion')).toBeFalsy()
    expect(getPanel('Second Accordion')).toBeVisible()
  })

  test('adheres to accessibility guidelines', async () => {
    const { container } = component

    expect(await axe(container)).toHaveNoViolations()
  })
})

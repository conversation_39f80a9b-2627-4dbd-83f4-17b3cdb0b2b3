.link {
  --gatx-link-font-size: unset;
  --gatx-link-color: unset;
  --gatx-link-border-color: unset;
  --gatx-link-border-radius: unset;

  --gatx-link-hover-bg-color: unset;
  --gatx-link-hover-color: unset;

  --gatx-link-focus-bg-color: unset;
  --gatx-link-focus-color: unset;

  --gatx-link-text-decoration: unset;
  --gatx-link-padding: unset;

  --gatx-link-outline-offset: unset;
  --gatx-link-outline-color: var(--gatx-colors-primary-7);

  @media (prefers-color-scheme: dark) {
    --gatx-link-outline-color: var(--gatx-colors-secondary-5);
  }

  padding: var(--gatx-link-padding);

  border: 1px solid var(--gatx-link-border-color);
  border-radius: var(--gatx-link-border-radius);

  color: var(--gatx-link-color);

  font-weight: var(--gatx-typography-font-weight-regular);
  font-size: var(--gatx-link-font-size);
  line-height: var(--gatx-typography-line-height);
  font-family: var(--gatx-typography-font-family);
  text-decoration: var(--gatx-link-text-decoration);

  transition:
    color 0.15s ease-in-out,
    background-color 0.15s ease-in-out;

  @media (prefers-reduced-motion: reduce) {
    transition: none;
  }

  &:hover,
  &:active {
    background-color: var(--gatx-link-hover-bg-color);
    color: var(--gatx-link-hover-color);
  }

  &:focus {
    outline: 4px solid
      color-mix(in srgb, var(--gatx-link-outline-color), transparent 60%);
    outline-offset: var(--gatx-link-outline-offset);
    background-color: var(--gatx-link-focus-bg-color);
    color: var(--gatx-link-focus-color);
  }
}

/** Variants */
.link-underline {
  --gatx-link-font-size: var(--gatx-typography-font-size-regular);
  --gatx-link-text-decoration: underline;
  --gatx-link-border-radius: 1px;

  --gatx-link-outline-offset: 4px;

  --gatx-link-color: var(--gatx-colors-primary-7);
  --gatx-link-hover-color: var(--gatx-colors-primary-4);
  --gatx-link-focus-color: var(--gatx-colors-primary-4);

  @media (prefers-color-scheme: dark) {
    --gatx-link-color: var(--gatx-colors-secondary-5);
    --gatx-link-hover-color: var(--gatx-colors-secondary-3);
    --gatx-link-focus-color: var(--gatx-colors-secondary-3);
  }
}

.link-outline {
  --gatx-link-font-size: var(--gatx-typography-font-size-sm);
  --gatx-link-text-decoration: none;
  --gatx-link-border-radius: 4px;

  --gatx-link-padding: var(--gatx-spacing-xs) var(--gatx-spacing-sm);

  --gatx-link-color: var(--gatx-colors-primary-7);
  --gatx-link-border-color: var(--gatx-colors-primary-7);

  --gatx-link-hover-color: var(--gatx-colors-primary-7);
  --gatx-link-hover-bg-color: var(--gatx-colors-primary-1);

  --gatx-link-focus-color: var(--gatx-colors-primary-7);
  --gatx-link-focus-bg-color: var(--gatx-colors-primary-1);

  @media (prefers-color-scheme: dark) {
    --gatx-link-color: var(--gatx-colors-light-1);
    --gatx-link-border-color: var(--gatx-colors-light-1);

    --gatx-link-hover-color: var(--gatx-colors-light-1);
    --gatx-link-hover-bg-color: var(--gatx-colors-dark-5);

    --gatx-link-focus-color: var(--gatx-colors-light-5);
    --gatx-link-focus-bg-color: var(--gatx-colors-dark-5);
  }
}

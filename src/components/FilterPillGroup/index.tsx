import { FilterPill, FilterPillProps } from './FilterPill'

type Props = {
  /**  Array of applied filters */
  appliedFilters: FilterPillProps[]
  /** Callback to remove a filter */
  removeFilter: (value: string, filterSource: string) => void
  /** Aria label for the group of filters */
  ariaLabel?: string
}

/**
 * FilterPillGroup component to display a group of selected filters.
 */
const FilterPillGroup = ({
  appliedFilters,
  removeFilter,
  ariaLabel = 'Selected filters',
}: Props) => {
  return (
    <ul className="filter-pill-group" aria-label={ariaLabel}>
      {appliedFilters.map(({ value, text, filterSource }) => (
        <li key={value}>
          <FilterPill
            value={value}
            text={text}
            filterSource={filterSource}
            removeFilter={removeFilter}
          />
        </li>
      ))}
    </ul>
  )
}

export { FilterPill, FilterPillGroup, type FilterPillProps }

import React, { ReactNode } from 'react'
import { Table as BootstrapTable } from 'react-bootstrap'

type Row = ReactNode | ReactNode[]

type Props = React.TableHTMLAttributes<HTMLTableElement> & {
  /**
   * An array of column headers, to be displayed in the `<thead>` of the table.
   * The number of headers should match the number of items in each row.
   */
  columnHeaders?: React.ReactNode[]
  /**
   * Indicates that the first element in each row is a header cell.
   * If there are more than two columns in a rowHeaders table, columnHeaders need to be provided for a11y but will be screen reader only.
   */
  hasRowHeaders?: boolean
  /** Indicates that the table should have sticky headers and columns, allowing for scrolling. */
  sticky?: boolean
  /** An array of row data to be shown in the `<tbody>` of the table. */
  rows: Row[]
}

const getRow = (row: Row, index: number, hasRowHeaders?: boolean) => {
  if (Array.isArray(row)) {
    return (
      <tr key={index}>
        {row.map((cell, i) => {
          const key = `${index}-${i}`
          return hasRowHeaders && i == 0 ? (
            <th scope="row" key={key}>
              {cell}
            </th>
          ) : (
            <td key={key}>{cell}</td>
          )
        })}
      </tr>
    )
  } else {
    if (React.isValidElement(row)) {
      return React.cloneElement(row, { key: index })
    }
  }
}

/**
 * The Table component is used to display information in a way that's easy to
 * scan, so that users can identify key pieces of information, and look for
 * patterns and insights across sets of data.
 *
 * A Table always contains rows of data. The contents of these rows should be
 * identified via column headers for structured data, or row headers for
 * unstructured data. See the supported properties for more information on the
 * intended use cases.
 *
 */
const Table = ({
  columnHeaders,
  hasRowHeaders,
  sticky,
  rows,
  ...props
}: Props) => {
  return (
    <BootstrapTable
      className={sticky ? 'sticky' : ''}
      striped
      borderless
      {...props}
    >
      {columnHeaders && (
        <thead className={hasRowHeaders ? 'sr-only' : ''}>
          <tr>
            {columnHeaders.map((cell, index) => (
              <th scope="col" key={index}>
                {cell}
              </th>
            ))}
          </tr>
        </thead>
      )}

      <tbody>
        {rows.map((row, index) => getRow(row, index, hasRowHeaders))}
      </tbody>
    </BootstrapTable>
  )
}

export { Table }

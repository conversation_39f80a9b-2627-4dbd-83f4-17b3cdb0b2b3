import { withActions } from '@storybook/addon-actions/decorator'
import type { Meta, StoryObj } from '@storybook/react'
import { ChoiceControl } from '.'

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta: Meta<typeof ChoiceControl> = {
  title: 'Components/ChoiceGroup/ChoiceControl',
  component: ChoiceControl,
  parameters: {
    layout: 'centered',
    actions: { handles: ['click'] },
  },
  decorators: [
    (Story) => (
      <div className="p-4 rounded docs-foreground">
        <Story />
      </div>
    ),
    withActions,
  ],
}

export default meta
type Story = StoryObj<typeof meta>

export const Small: Story = {
  name: 'Size: Small',
  parameters: {
    docs: {
      description: {
        story:
          'Small chips should be used for actions within the software such as resizing a panel.',
      },
    },
  },
  args: {
    type: 'radio',
    size: 'small',
    label: 'Small Chip',
    name: 'small-chip',
    value: 'SmChip',
  },
}

export const Large: Story = {
  name: 'Size: Large',
  parameters: {
    docs: {
      description: {
        story: 'Large chips should be used within forms.',
      },
    },
  },
  args: {
    type: 'radio',
    size: 'large',
    label: 'Large Chip',
    name: 'large-chip',
    value: 'LgChip',
  },
}

export const Checkbox: Story = {
  name: 'Type: Checkbox',
  parameters: {
    docs: {
      description: {
        story:
          'Chips with the type `checkbox` should be used when the user is able to select multiple options.',
      },
    },
  },
  args: {
    type: 'checkbox',
    size: 'small',
    label: 'Checkbox Chip',
    value: 'checkbox-chip',
    name: 'checkboxes',
  },
}

export const Radio: Story = {
  name: 'Type: Radio',
  parameters: {
    docs: {
      description: {
        story:
          'Chips with the type `radio` should be used when the user should only select one option.',
      },
    },
  },
  args: {
    type: 'radio',
    size: 'small',
    label: 'Radio Chip',
    value: 'radio-chip',
    name: 'radios',
  },
}

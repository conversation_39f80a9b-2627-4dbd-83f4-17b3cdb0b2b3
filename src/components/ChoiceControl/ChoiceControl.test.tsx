import { render, screen } from '@/tests/react'
import { axe } from 'jest-axe'
import { ChoiceControl } from '.'

describe('Chip', () => {
  test('renders the single select chip with provided props', () => {
    const el = render(
      <ChoiceControl
        type="radio"
        size="small"
        disabled={false}
        label="Test Chip"
        name="testGroup"
        value="testValue"
        onChange={() => {
          console.log('--')
        }}
      />,
    )

    const chip = el.getByRole('radio', { name: 'Test Chip' })

    expect(chip).toHaveAttribute('type', 'radio')
  })

  test('renders the multi select chip with provided props', () => {
    render(
      <ChoiceControl
        type="checkbox"
        size="small"
        disabled={false}
        label="Test Chip"
        name="testGroup"
        value="testValue"
        onChange={() => {
          console.log('--')
        }}
      />,
    )

    const chip = screen.getByRole('checkbox', { name: 'Test Chip' })

    expect(chip).toHaveAttribute('type', 'checkbox')
  })

  test('adheres to accessibility guidelines', async () => {
    const { container } = render(
      <ChoiceControl
        type="radio"
        size="small"
        disabled={false}
        label="Test Chip"
        name="testGroup"
        value="testValue"
        onChange={() => {
          console.log('--')
        }}
      />,
    )
    expect(await axe(container)).toHaveNoViolations()
  })
})

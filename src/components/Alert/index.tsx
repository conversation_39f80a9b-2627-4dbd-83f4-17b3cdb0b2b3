import clsx from 'clsx'
import React, { ReactNode } from 'react'
import {
  IconProps as BootstrapIconProps,
  CheckCircleFill,
  ExclamationDiamondFill,
  ExclamationTriangleFill,
  InfoCircleFill,
} from 'react-bootstrap-icons'
import Bootstrap<PERSON>lert from 'react-bootstrap/Alert'
import { AlertHeadingProps } from 'react-bootstrap/AlertHeading'
import { Badge } from '../Badge'
import { CloseButton } from '../CloseButton'

type Props = AlertHeadingProps & {
  /** The title of the alert, which should catch the attention of the user first */
  title?: string
  /** The actual content of the alert */
  children: ReactNode
  /** The variant of the alert */
  level: 'error' | 'warning' | 'info' | 'success'
  /** The urgency of the alert */
  urgency: 'immediate' | 'deferred' | 'inline'
  /** The content of a badge to be shown instead of the icon */
  badge?: string
  /** Whether the alert should have compact layout */
  compact?: boolean
  /** Whether the alert is dismissable with a close button */
  dismmissable?: boolean
  /** The function to call when the alert
   * is dismissed. Only applicable if `dismissable` is true */
  onDismiss?: React.MouseEventHandler<HTMLButtonElement>
}

const VARIANT_ICONS: Record<Props['level'], React.FC<BootstrapIconProps>> = {
  error: ExclamationTriangleFill,
  warning: ExclamationDiamondFill,
  info: InfoCircleFill,
  success: CheckCircleFill,
}

const URGENCY_ROLE: Record<Props['urgency'], 'alert' | 'status' | undefined> = {
  immediate: 'alert',
  deferred: 'status',
  inline: undefined,
}

/**
 * The Alert component provides contextual feedback messages for typical user actions and system state with the handful of available and flexible alert messages.
 * Alerts can be configured to have different layouts, notification levels and urgencies. While the first two have visual-only implications, the urgency affects the role of the alert in the accessibility tree:
 *
 * - **Immediate**: the alert will be announced immediately to the user, interrupting their current task. Implies `role="alert"`.
 * - **Deferred**: the alert will be announced when the user is idle, allowing them to finish their current task. Implies `role="status"`.
 * - **Inline**: the alert will not receive special announcement, so it will be read as part of the normal flow of the page. Implies no special role.
 */
const Alert = ({
  title,
  level,
  urgency,
  children,
  compact,
  badge,
  dismmissable = false,
  onDismiss,
  ...props
}: Props) => {
  const Icon = VARIANT_ICONS[level]

  return (
    <BootstrapAlert
      className={clsx(
        `alert-${level}`,
        { 'alert-compact': compact },
        props.className,
      )}
      role={URGENCY_ROLE[urgency]}
    >
      {badge ? (
        <Badge label={badge} variant={level} />
      ) : (
        <Icon className="icon" role="presentation" />
      )}
      <div className="alert-content flex-1">
        {title && (
          <BootstrapAlert.Heading as={props.as}>{title}</BootstrapAlert.Heading>
        )}
        <div className="alert-body">{children}</div>
      </div>
      {dismmissable && onDismiss && (
        <>
          <CloseButton
            onClick={onDismiss}
            className="btn-close"
            type="button"
            aria-label="Close"
          />
        </>
      )}
    </BootstrapAlert>
  )
}

export { Alert }

import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { Tabs } from '.'

const meta: Meta<typeof Tabs.Container> = {
  title: 'Components/Tabs',
  component: Tabs.Container,
  parameters: {
    layout: 'padded',
    actions: { handles: ['click'] },
  },
  subcomponents: { Pane: Tabs.Pane as React.ComponentType<unknown> },
}

export default meta
type Story = StoryObj<typeof meta>

export const Underline: Story = {
  name: 'Variant: Underline',
  parameters: {
    docs: {
      description: {
        story: 'Underline variant tabs are used for top-level navigation.',
      },
    },
  },
  args: {
    variant: 'underline',
    id: 'tab-example-underline',
    defaultActiveKey: 'test-1',
    children: [
      <Tabs.Pane title="Tab One" key="test-1">
        <p>This is some content for tab one.</p>
      </Tabs.Pane>,
      <Tabs.Pane title="Tab Two" key="test-2">
        <p>And here&apos;s next content for tab two.</p>
      </Tabs.Pane>,
      <Tabs.Pane title="Tab Three" key="test-3">
        <p>And here&apos;s next content for tab three.</p>
      </Tabs.Pane>,
    ],
  },
}

export const Pill: Story = {
  name: 'Variant: Pill',
  parameters: {
    docs: {
      description: {
        story: `Pill variant tabs are used for secondary navigation, in
          situations involving complex information hierarchies.`,
      },
    },
  },
  args: {
    variant: 'pills',
    id: 'tab-example-pills',
    defaultActiveKey: 'test-1',
    children: [
      <Tabs.Pane title="Tab One" key="test-1">
        <p>This is some content for tab one.</p>
      </Tabs.Pane>,
      <Tabs.Pane title="Tab Two" key="test-2">
        <p>And here&apos;s next content for tab two.</p>
      </Tabs.Pane>,
    ],
  },
}

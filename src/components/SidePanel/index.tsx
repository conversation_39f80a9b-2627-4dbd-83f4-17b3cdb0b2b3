import { useI18n } from '@/i18n/useI18n'
import { createContext } from 'react'
import { ChoiceGroup } from '../ChoiceGroup'
import { CloseButton } from '../CloseButton'
import { Heading, Landmark } from '../Content'

const InSidePanelContext = createContext<boolean>(false)

const SIZE_OPTIONS = [
  {
    label: 'Full',
    ariaLabel: 'Full Screen (Hides Main Content)',
    value: 'full',
  },
  {
    label: '1/2',
    ariaLabel: 'Half Screen',
    value: 'half',
  },
  {
    label: '1/3',
    ariaLabel: 'One Third Screen',
    value: 'third',
  },
]

type Size = (typeof SIZE_OPTIONS)[number]['value']

type Props = {
  /** The side panel title. Appears as the heading. */
  title: string
  /** The side panel content. */
  children: React.ReactNode
  /** Callback to handle closing the side panel. */
  onClose?: () => void
  /** Callback to handle resizing the side panel. */
  onResize?: (size: Size) => void
  /** The width of the side panel.  */
  size?: Size
  /** The id of the side panel */
  id?: string
}

/**
 * The `SidePanel` component to be composed in multiple-column layouts as a side
 * panel.
 *
 * Supports closing and resizing interactions and automatically integrates with
 * `Tabs` component if these are placed as the `SidePanel`'s first child.
 *
 * Examples below showcase these features.
 */
const SidePanel = ({
  title,
  children,
  onResize,
  onClose,
  size = 'third',
  id,
}: Props) => {
  return (
    <Landmark
      id={id}
      as="aside"
      className="side-panel"
      level={2}
      data-side-panel="true"
    >
      <header className="side-panel-header">
        <Heading className="text-title-sm">{title}</Heading>
        <div className="side-panel-controls">
          {onResize && <SizeInput onChange={onResize} selected={size} />}
          {onClose && (
            <CloseButton
              onClick={onClose}
              aria-label="Close Side Panel"
              aria-controls={id}
              aria-expanded="true"
            />
          )}
        </div>
      </header>
      <InSidePanelContext.Provider value={true}>
        <div className="side-panel-content">{children}</div>
      </InSidePanelContext.Provider>
    </Landmark>
  )
}

const SizeInput = ({
  onChange,
  selected,
}: {
  onChange: (size: Size) => void
  selected: Size
}) => {
  const { t } = useI18n('SidePanel')
  return (
    <ChoiceGroup
      value={selected}
      size="small"
      multiSelect={false}
      label={t('panelWidthLabel')}
      labelScreenreaderOnly
      options={SIZE_OPTIONS}
      onChange={onChange}
    />
  )
}

export { InSidePanelContext, SidePanel, SizeInput }
export type { Size as SidePanelSize }

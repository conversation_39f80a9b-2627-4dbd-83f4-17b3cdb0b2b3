import type { Meta } from '@storybook/react'
import { Badge } from '.'

const meta: Meta<typeof Badge> = {
  title: 'Components/Badge',
  component: Badge,
  parameters: {
    layout: 'centered',
  },
}

export default meta

export const Default: Meta<typeof Badge> = {
  component: Badge,
  args: {
    label: 'Badge',
  },
}

/**
 * The `Badge` supports six different variants: `primary`, `outline`, `info`, `warning`, `error`, and `success`.
 */
export const Variants: Meta<typeof Badge> = {
  argTypes: {
    variant: { control: false },
  },
  render: ({ label, ...args }) => (
    <div className="flex gap-sm">
      {(
        [
          'primary',
          'outline',
          'info',
          'warning',
          'error',
          'success',
          'disabled',
        ] as const
      ).map((variant) => (
        <Badge
          {...args}
          key={variant}
          label={label || variant.charAt(0).toUpperCase() + variant.slice(1)}
          variant={variant}
        />
      ))}
    </div>
  ),
}

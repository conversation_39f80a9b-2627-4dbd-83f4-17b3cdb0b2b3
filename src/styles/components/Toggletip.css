/**
* Base Component Definition
*/

.btn-toggletip.btn {
  --gatx-btn-bg: transparent;
  --gatx-btn-border-color: none;
  --gatx-btn-color: var(--gatx-colors-light-9);
  --gatx-btn-border-radius: 50%;
  --gatx-btn-padding: 0;
  --gatx-btn-font-size: 1rem;

  /* Center the icon perfectly */
  display: flex;
  align-items: center;
  justify-content: center;

  /* Button dimensions for circular shape */
  width: 1rem;
  height: 1rem;
  margin: 0;
  padding: 0;

  /* Remove any default button styling that could affect alignment */
  border: none;

  @media (prefers-color-scheme: dark) {
    --gatx-btn-color: var(--gatx-colors-dark-2);
  }

  .icon {
    width: 1rem;
    height: 1rem;

    /* Remove built-in SVG padding to make icon fill button exactly */
    overflow: visible;
    color: var(--gatx-btn-color);

    /* Scale the SVG content to fill the container completely */
    svg {
      width: 100%;
      height: 100%;
      transform: scale(1.2);
    }
  }

  &:hover {
    --gatx-btn-color: var(--gatx-colors-dark-8);
  }

  &:focus {
    --gatx-btn-color: var(--gatx-colors-dark-8);
    outline: none;
  }
}

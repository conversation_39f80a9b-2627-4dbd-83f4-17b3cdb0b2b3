import { SVGProps } from 'react'

const Train = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="131"
    height="85"
    fill="none"
    viewBox="0 0 131 85"
    {...props}
  >
    <rect
      width="56"
      height="40"
      x="4"
      y="3"
      className="stroke-[#A6A7A8] dark:stroke-[#707B82]"
      strokeWidth="6"
      rx="4"
    ></rect>
    <rect
      width="55"
      height="40"
      x="73"
      y="3"
      className="stroke-[#A6A7A8] dark:stroke-[#707B82]"
      strokeWidth="6"
      rx="4"
    ></rect>
    <rect
      width="130"
      height="9"
      y="53.1"
      className="fill-[#A6A7A8] dark:fill-[#707B82]"
      rx="2"
    ></rect>
    <path
      className="stroke-[#A6A7A8] dark:stroke-[#707B82]"
      strokeLinecap="round"
      strokeWidth="5.5"
      d="M16 15.6v15M27 15.6v15M38 15.6v15M49 15.6v15M84 15.6v15M95 15.6v15M106 15.6v15M117 15.6v15"
    ></path>
    <circle
      cx="15.5"
      cy="73.6"
      r="8.5"
      className="fill-[#818182] dark:fill-[#9DA4A9] stroke-[#EAEBEC] dark:stroke-[#1E2931]"
      strokeWidth="4"
    ></circle>
    <ellipse
      cx="82"
      cy="73.6"
      className="fill-[#818182] dark:fill-[#9DA4A9] stroke-[#EAEBEC] dark:stroke-[#1E2931]"
      strokeWidth="4"
      rx="9"
      ry="8.5"
    ></ellipse>
    <ellipse
      cx="47"
      cy="73.6"
      className="fill-[#818182] dark:fill-[#9DA4A9] stroke-[#EAEBEC] dark:stroke-[#1E2931]"
      strokeWidth="4"
      rx="9"
      ry="8.5"
    ></ellipse>
    <circle
      cx="112.5"
      cy="73.6"
      r="8.5"
      className="fill-[#818182] dark:fill-[#9DA4A9] stroke-[#EAEBEC] dark:stroke-[#1E2931]"
      strokeWidth="4"
    ></circle>
  </svg>
)

export { Train }

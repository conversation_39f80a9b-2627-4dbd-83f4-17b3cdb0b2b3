import { createEventKey } from '@/utils/createEventKey'
import { useId } from 'react'
import {
  AccordionBodyProps,
  Accordion as BootstrapAccordion,
} from 'react-bootstrap'
import { ChevronDown } from 'react-bootstrap-icons'
import { Heading, Landmark } from '../Content'

export type Props = AccordionBodyProps & {
  /** The user-readable name for the accordion. */
  title: string
  /** Additional content to render in the header. */
  indicators?: React.ReactNode
  /** Whether the accordion is disabled */
  disabled?: boolean
}

const Item = ({
  title,
  indicators,
  children,
  disabled,
  className,
  ...props
}: Props) => {
  const id = useId()
  const headingId = `${id}-heading`
  const panelId = `${id}-panel`

  const eventKey = createEventKey(title)

  return (
    <BootstrapAccordion.Item eventKey={eventKey} className={className}>
      <Landmark virtual>
        <Heading id={headingId} className="accordion-header">
          <BootstrapAccordion.Button
            aria-controls={panelId}
            disabled={disabled}
          >
            {title}
            <div className="accordion-indicators">{indicators}</div>
            <ChevronDown className="icon" aria-hidden />
          </BootstrapAccordion.Button>
        </Heading>
        <BootstrapAccordion.Collapse
          role="region"
          id={panelId}
          eventKey={eventKey}
          aria-labelledby={headingId}
        >
          <div {...props} className="accordion-body">
            {children}
          </div>
        </BootstrapAccordion.Collapse>
      </Landmark>
    </BootstrapAccordion.Item>
  )
}

export { Item }

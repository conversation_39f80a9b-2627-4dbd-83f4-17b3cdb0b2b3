import { ChoiceGroup } from '@/components/ChoiceGroup'
import { useI18n } from '@/i18n/useI18n'
import {
  DARK_THEME_CLASS,
  LIGHT_THEME_CLASS,
} from '@/stores/ColorScheme/constants'
import { useColorScheme } from '@/stores/ColorScheme/useColorScheme'

const colorModeOptions = [
  {
    label: 'Device Default',
    value: 'device',
  },
  {
    label: 'Dark Mode',
    value: DARK_THEME_CLASS,
  },
  {
    label: 'Light Mode',
    value: LIGHT_THEME_CLASS,
  },
]

export type ColorModeOption = (typeof colorModeOptions)[number]['value']

const ColorMode = () => {
  const { t } = useI18n('SiteHeader')
  const [colorScheme = 'device', setColorScheme] = useColorScheme()

  function handleChange(value: ColorModeOption) {
    setColorScheme(value === 'device' ? undefined : value)
  }

  return (
    <ChoiceGroup
      label={t('colorMode')}
      layout="vertical"
      value={colorScheme}
      onChange={handleChange}
      variant="classic"
      multiSelect={false}
      options={colorModeOptions}
    />
  )
}

export { ColorMode }

import type { <PERSON>a, StoryObj } from '@storybook/react'
import { Alert } from './index'

const meta: Meta<typeof Alert> = {
  title: 'Components/Alert',
  component: Alert,
  parameters: {
    layout: 'padded',
  },
  args: {
    title: 'A simple alert',
    children: 'check it out!',
    level: 'error',
    urgency: 'immediate',
    compact: true,
  },
}

export default meta

type Story = StoryObj<typeof Alert>

export const Default: Story = {}

export const Levels: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Alert levels are a visual aid to the meaning, context or urgency of the message that is being displayed. Use `error` for critical alerts, such as system errors or danger notifications, and `warning` for less critical alerts. Use `info` for general status notifications.',
      },
    },
  },
  argTypes: {
    level: { control: { disable: true } },
  },
  render: (args) => (
    <div className="space-y-md">
      <Alert {...args} level="error" />
      <Alert {...args} level="warning" />
      <Alert {...args} level="info" />
      <Alert {...args} level="success" />
    </div>
  ),
}

export const Layouts: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'The `compact` layout allows the title and the content to be displayed in a single line. Prefer this layout when the content is short.',
      },
    },
  },
  argTypes: {
    compact: { control: { disable: true } },
  },
  args: {
    title: 'Well done!',
    children: `Aww yeah, you successfully read this important alert message.
      This example text is going to run a bit longer so that you can see
      how spacing within an alert works with this kind of content.`,
    level: 'error',
    urgency: 'immediate',
  },

  render: (args) => (
    <div className="space-y-md">
      <div className="font-bold text-title-xs">Compact</div>
      <Alert {...args} compact />
      <div className="font-bold text-title-xs">Default</div>
      <Alert {...args} compact={false} />
    </div>
  ),
}

export const ArbitraryContent: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Arbitrary content can be passed to `Alert` as `children`. For example, a whole `dl` can be rendered inside the alert.',
      },
    },
  },
  args: {
    title: "This alert's content is a definition list",
    children: (
      <dl>
        <dt id="alert-dt">
          This is a <span className="font-bold">dt</span>
        </dt>
        <dd aria-labelledby="alert-dt">
          This is a <span className="font-bold">dd</span> related to the dt
          above
        </dd>
      </dl>
    ),
    level: 'warning',
    urgency: 'deferred',
    compact: false,
  },
}

export const CustomBadge: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'A custom badge can be configured instead of the default icon. This can be useful to add more context to the alert. It is advised to use this option with `deferred` urgency. Using a custom badge with other urgencies is allowed but out of the design system.',
      },
    },
  },
  args: {
    level: 'warning',
    urgency: 'deferred',
    badge: 'Corrosive',
    compact: false,
  },
}

import { withActions } from '@storybook/addon-actions/decorator'
import type { Meta, StoryObj } from '@storybook/react'
import * as Icon from 'react-bootstrap-icons'
import { But<PERSON> } from '.'

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta: Meta<typeof Button> = {
  title: 'Components/Button',
  component: Button,
  parameters: {
    layout: 'centered',
    actions: { handles: ['click'] },
  },
  decorators: [
    (Story) => (
      <div className="docs-foreground p-sm">
        <Story />
      </div>
    ),
    withActions,
  ],
  argTypes: {
    icon: {
      control: 'select',
      options: Object.keys(Icon),
      table: { type: { summary: 'string' } },
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Primary: Story = {
  name: 'Variant: Primary',
  parameters: {
    docs: {
      description: {
        story:
          'Primary buttons are used any place we want to call attention to an action.',
      },
    },
  },
  args: {
    text: 'Button Text',
    variant: 'primary',
    size: 'medium',
  },
}

export const Outline: Story = {
  name: 'Variant: Outline',
  parameters: {
    docs: {
      description: {
        story: `Outline buttons are used any place we want to support frequent
          interactions that should not be the primary focus of a view.`,
      },
    },
  },
  args: {
    text: 'Button Text',
    variant: 'outline',
  },
}

export const Text: Story = {
  name: 'Variant: Text',
  parameters: {
    docs: {
      description: {
        story: `Text buttons are used any place we want to support infrequent
          interactions, or actions initiated outside of the scope of the primary
          content viewing area.`,
      },
    },
  },
  args: {
    text: 'Button Text',
    variant: 'text',
  },
}

export const Neutral: Story = {
  name: 'Variant: Neutral',
  parameters: {
    docs: {
      description: {
        story: `Neutral buttons are commonly used for page actions and search filters.
        While initially designed for icon usage, all button variants can include icons.
        These buttons are typically placed across the top of pages and maintain a visually subtle appearance.`,
      },
    },
  },
  args: {
    text: 'Button Text',
    variant: 'neutral',
    icon: 'PlusSquare',
  },
}

export const Small: Story = {
  name: 'Size: Small',
  parameters: {
    docs: {
      description: {
        story:
          'Small buttons are used when we need to embed an action in a compact view.',
      },
    },
  },
  args: {
    text: 'Button Text',
    size: 'small',
  },
}

export const Large: Story = {
  name: 'Size: Large',
  parameters: {
    docs: {
      description: {
        story: `Large buttons are used when we want a button to be prominent in
          the view for either increased accessibility or to call extra attention
          to them.`,
      },
    },
  },
  args: {
    text: 'Button Text',
    size: 'large',
  },
}

import { use, useId } from 'react'
import { HeadingIdContext, HeadingLevelContext } from './Context'

type Props = React.HTMLAttributes<HTMLDivElement> & {
  /** The landmark element type to wrap around the content. */
  as?: 'main' | 'section' | 'aside' | 'nav'
  /** Should the wrapper HTML element be omitted? */
  virtual?: boolean
  /** An initial level for heading elements, if the default is inappropriate. */
  level?: number
  children?: React.ReactNode
}

/**
 * The Content component offers a set of utility elements for semantic content
 * organization. It orchestrates some behind-the-scenes context providers to
 * support automatic population of heading levels and accessible labels for
 * semantic HTML landmarks. The component consists of two elements that are
 * intended to be used in tandem:
 *
 * - `Content.Landmark` is a parent component that represents an [accessibility
 *   landmark][1] on the page. Each landmark increments the level for the
 *   heading element(s) rendered within, and adds a heading ID to the context
 *   that will automatically associate the element with its child heading.
 * - `Content.Heading` is a utility wrapper component that applies the proper
 *   ARIA properties to define a heading at the appropriate level for its parent
 *   landmark by default.
 *
 * The expectation is that each non-virtual Landmark (see below) at some point
 * has a single Heading rendered within it.
 *
 * [1]: https://www.w3schools.com/accessibility/accessibility_landmarks.php
 */
export const Landmark = ({
  id,
  as: Element = 'section',
  virtual = false,
  level,
  ...props
}: Props) => {
  const defaultId = useId()
  const headingId = id ?? defaultId
  const headingLevel = level ?? use(HeadingLevelContext) + 1

  return (
    <HeadingIdContext.Provider value={headingId}>
      <HeadingLevelContext.Provider value={headingLevel}>
        {virtual ? (
          props.children
        ) : (
          <Element aria-labelledby={headingId} {...props} />
        )}
      </HeadingLevelContext.Provider>
    </HeadingIdContext.Provider>
  )
}

import { render } from '@/tests/react'
import { axe } from 'jest-axe'
import { BaseBadge } from './BaseBadge'

const VARIANTS = [
  'primary',
  'outline',
  'info',
  'warning',
  'error',
  'success',
  'disabled',
] as const

const SIZES = ['sm', 'md', 'lg'] as const

const TEST_CASES = VARIANTS.flatMap((variant) =>
  SIZES.map((size) => ({ variant, size })),
)

describe('Badge', () => {
  test.each(TEST_CASES)(
    'adheres to accessibility guidelines ($variant $size)',
    async ({ variant, size }) => {
      const { container } = render(
        <BaseBadge label="Badge Text" variant={variant} size={size} />,
      )
      expect(await axe(container)).toHaveNoViolations()
    },
  )
})

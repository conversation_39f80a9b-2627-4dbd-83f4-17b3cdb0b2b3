import { FC } from 'react'
import { BadgeVariant, BaseBadge } from './BaseBadge'

type Props = {
  /** Label to display in the Badge, also used as aria-label */
  label: string
  /** The specific style of Badge to render */
  variant?: BadgeVariant
}

/**
 * The `Badge` is used to display high level information or status.
 */
const Badge: FC<Props> = (props) => <BaseBadge {...props} />

export { Badge, type BadgeVariant }

import { FC } from 'react'
import {
  Anchor,
  Dropdown as <PERSON><PERSON><PERSON><PERSON><PERSON>down,
  Button,
  DropdownItemProps,
} from 'react-bootstrap'
import * as Icons from 'react-bootstrap-icons'

// Icon names available for use in icon buttons
type IconName = keyof typeof Icons

type BaseDropdownItem = {
  /** The title of the dropdown item that will display on the menu */
  label: string
  /** Whether the dropdown item is selected */
  selected?: boolean
  /**
   * The icon to render within the menu item, if any.
   * Access https://icons.getbootstrap.com/ to see the list of icons.
   * The icon name should be passed as a string, using PascalCase convention e.g. 'PlusSquare' or 'ArrowLeftShort'.
   */
  icon?: IconName
  /**
   * The component to use for the menu item. Defaults to Anchor or Button,
   * depending on the behavior of the element being rendered.
   */
  Component?: FC<Partial<DropdownItemProps>>
}

type LinkDropdownItem = {
  href: string
} & BaseDropdownItem

type ButtonDropdownItem = {
  onClick: () => void
} & BaseDropdownItem

type DropDownItem = LinkDropdownItem | ButtonDropdownItem

/**
 * The DropDown.Item component represents a single navigation links or button
 * action to present to the user.
 *
 * It must be defined with either an `href` or `onClick` prop, which will
 * determine the type of element rendered and its behavior. The rendered
 * component can be customized with the `Component` prop, for use cases like DOM
 * routing, where a different linking implementation is needed.
 */
const Item = ({
  label,
  selected,
  icon,
  Component,
  ...action
}: DropDownItem) => {
  const Icon = icon ? Icons[icon] : null

  const props: Partial<DropdownItemProps> =
    'href' in action
      ? { href: (action as LinkDropdownItem).href, as: Component ?? Anchor }
      : {
          onClick: (action as ButtonDropdownItem).onClick,
          as: Component ?? Button,
        }

  return (
    <BootstrapDropdown.Item
      className="dropdown-item flex items-center"
      aria-label={label}
      role="menuitem"
      aria-current={selected ?? false}
      {...props}
    >
      {Icon && <Icon aria-hidden className="icon dropdown-item-icon" />}
      {label}
    </BootstrapDropdown.Item>
  )
}

export { Item }
export type { DropDownItem }

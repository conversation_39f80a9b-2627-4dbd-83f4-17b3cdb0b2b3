/**
* Base Component Definition
*/

.btn {
  --gatx-btn-padding: 6px 12px;
  --gatx-btn-border-color: unset;
  --gatx-btn-border-radius: var(--gatx-border-radius);
  --gatx-btn-bg: transparent;
  --gatx-btn-box-shadow-color: color-mix(
    in srgb,
    var(--gatx-colors-primary-7),
    transparent 60%
  );
  --gatx-btn-box-shadow: 0px 0px 0px 4px var(--gatx-btn-box-shadow-color);
  --gatx-btn-color: unset;
  --gatx-btn-font-size: var(--gatx-typography-font-size-regular);

  --gatx-btn-active-border-color: unset;
  --gatx-btn-active-bg: unset;
  --gatx-btn-active-color: unset;

  --gatx-btn-hover-border-color: unset;
  --gatx-btn-hover-bg: unset;
  --gatx-btn-hover-color: unset;

  --gatx-btn-disabled-border-color: unset;
  --gatx-btn-disabled-bg: unset;
  --gatx-btn-disabled-color: unset;

  .icon {
    display: flex;
    flex: 0 0 auto;
    width: var(--gatx-btn-font-size * 1.15);
    margin-top: 0.15rem;
  }

  @media (prefers-color-scheme: dark) {
    --gatx-btn-box-shadow-color: color-mix(
      in srgb,
      var(--gatx-colors-secondary-5),
      transparent 60%
    );
  }

  display: inline-flex;
  align-items: center;
  padding: var(--gatx-btn-padding);
  gap: var(--gatx-spacing-sm);
  border: 1px solid var(--gatx-btn-border-color);
  border-radius: var(--gatx-btn-border-radius);
  background-color: var(--gatx-btn-bg);
  color: var(--gatx-btn-color);

  font-weight: 400;
  font-size: var(--gatx-btn-font-size);
  line-height: var(--gatx-typography-line-height);
  font-family: var(--gatx-typography-font-family);

  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  transition:
    color 0.15s ease-in-out,
    background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;

  @media (prefers-reduced-motion: reduce) & {
    transition: none;
  }

  &:hover {
    border-color: var(--gatx-btn-hover-border-color);
    background-color: var(--gatx-btn-hover-bg);
    color: var(--gatx-btn-hover-color);
  }

  &:focus-visible {
    border-color: var(--gatx-btn-hover-border-color);
    outline: none;
    background-color: var(--gatx-btn-hover-bg);
    box-shadow: var(--gatx-btn-box-shadow);
    color: var(--gatx-btn-hover-color);
  }

  &:first-child:active,
  &.active,
  &.show {
    border-color: var(--gatx-btn-active-border-color);
    background-color: var(--gatx-btn-active-bg);
    color: var(--gatx-btn-active-color);
  }

  &:active:focus-visible,
  &.active:focus-visible,
  &.show:focus-visible {
    box-shadow: var(--gatx-btn-box-shadow);
  }

  &:disabled,
  &.disabled,
  fieldset:disabled & {
    border-color: var(--gatx-btn-disabled-border-color);
    background-color: var(--gatx-btn-disabled-bg);
    color: var(--gatx-btn-disabled-color);
    opacity: 0.65;
    pointer-events: none;
  }
}

/**
* Variants - Style
*/

.btn-primary {
  --gatx-btn-border-color: var(--gatx-color-primary);
  --gatx-btn-bg: var(--gatx-color-primary);
  --gatx-btn-color: var(--gatx-colors-light-1);

  --gatx-btn-active-border-color: var(--gatx-btn-active-bg);
  --gatx-btn-active-bg: var(--gatx-color-primary-dark);
  --gatx-btn-active-color: var(--gatx-colors-light-1);

  --gatx-btn-hover-border-color: var(--gatx-btn-hover-bg);
  --gatx-btn-hover-bg: var(--gatx-color-primary-dark);
  --gatx-btn-hover-color: var(--gatx-colors-light-1);

  --gatx-btn-disabled-border-color: var(--gatx-btn-disabled-bg);
  --gatx-btn-disabled-bg: var(--gatx-colors-light-7);
  --gatx-btn-disabled-color: var(--gatx-colors-light-9);

  @media (prefers-color-scheme: dark) {
    --gatx-btn-disabled-bg: var(--gatx-colors-dark-4);
    --gatx-btn-disabled-color: var(--gatx-colors-dark-8);
  }
}

.btn-outline {
  --gatx-btn-border-color: var(--gatx-btn-color);
  --gatx-btn-color: var(--gatx-color-primary);

  --gatx-btn-active-border-color: var(--gatx-btn-active-color);
  --gatx-btn-active-bg: var(--gatx-colors-primary-1);
  --gatx-btn-active-color: var(--gatx-color-primary);

  --gatx-btn-hover-border-color: var(--gatx-btn-hover-color);
  --gatx-btn-hover-bg: var(--gatx-colors-primary-1);
  --gatx-btn-hover-color: var(--gatx-color-primary);

  --gatx-btn-disabled-border-color: var(--gatx-btn-disabled-color);
  --gatx-btn-disabled-color: var(--gatx-colors-light-8);

  @media (prefers-color-scheme: dark) {
    --gatx-btn-color: var(--gatx-colors-light-1);

    --gatx-btn-active-bg: var(--gatx-colors-dark-5);
    --gatx-btn-active-color: var(--gatx-colors-light-1);

    --gatx-btn-hover-bg: var(--gatx-colors-dark-5);
    --gatx-btn-hover-color: var(--gatx-colors-light-1);

    --gatx-btn-disabled-color: var(--gatx-colors-dark-4);
  }
}

.btn-text {
  --gatx-btn-color: var(--gatx-color-primary);

  --gatx-btn-active-bg: var(--gatx-colors-light-3);
  --gatx-btn-active-color: var(--gatx-color-primary);

  --gatx-btn-hover-bg: var(--gatx-colors-light-3);
  --gatx-btn-hover-color: var(--gatx-color-primary);

  --gatx-btn-disabled-color: var(--gatx-colors-light-8);

  @media (prefers-color-scheme: dark) {
    --gatx-btn-color: var(--gatx-colors-secondary-5);

    --gatx-btn-active-bg: var(--gatx-colors-dark-9);
    --gatx-btn-active-color: var(--gatx-colors-secondary-5);

    --gatx-btn-hover-bg: var(--gatx-colors-dark-9);
    --gatx-btn-hover-color: var(--gatx-colors-secondary-5);

    --gatx-btn-disabled-color: var(--gatx-colors-dark-4);
  }
}

.btn-neutral {
  --gatx-btn-color: var(--gatx-colors-dark-5);

  --gatx-btn-active-bg: var(--gatx-colors-light-3);
  --gatx-btn-active-color: var(--gatx-colors-dark-1-dark-5);

  --gatx-btn-hover-bg: var(--gatx-colors-light-3);
  --gatx-btn-hover-color: var(--gatx-colors-dark-5);

  --gatx-btn-disabled-color: var(--gatx-colors-light-8);

  @media (prefers-color-scheme: dark) {
    --gatx-btn-color: var(--gatx-colors-dark-2);

    --gatx-btn-active-bg: var(--gatx-colors-dark-9);
    --gatx-btn-active-color: var(--gatx-colors-dark-2);

    --gatx-btn-hover-bg: var(--gatx-colors-dark-9);
    --gatx-btn-hover-color: var(--gatx-colors-dark-2);

    --gatx-btn-disabled-color: var(--gatx-colors-dark-4);
  }
}

/**
* Variants - Size
*/

.btn-sm {
  --gatx-btn-padding: var(--gatx-spacing-xs) var(--gatx-spacing-sm);
  --gatx-btn-border-radius: calc(var(--gatx-border-radius) * 0.8);
  --gatx-btn-font-size: var(--gatx-typography-font-size-sm);
}

.btn-lg {
  --gatx-btn-padding: var(--gatx-spacing-sm) var(--gatx-spacing-md);
  --gatx-btn-border-radius: calc(var(--gatx-border-radius) * 1.2);
  --gatx-btn-font-size: var(--gatx-typography-font-size-lead);
}

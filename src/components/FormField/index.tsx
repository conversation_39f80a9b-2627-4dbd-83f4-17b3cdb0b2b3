import clsx from 'clsx'
import { cloneElement, ReactElement, useId } from 'react'
import { Asterisk, ExclamationCircle } from 'react-bootstrap-icons'

type InputProps = {
  id?: string
  'aria-describedby'?: string
  'aria-invalid'?: boolean
  'aria-label'?: string
  readOnly?: boolean
  value?: unknown
}

type Props = {
  /** The label text for the input. */
  label: string
  /** Whether the legend should be visually hidden. */
  labelScreenreaderOnly?: boolean
  /** The description or helper text for the input. */
  description?: string
  /** The error message text for the input. */
  error?: string
  /** The warning message text for the input. */
  warning?: string
  /** The rendered input to be wrapped within the form field. */
  children: ReactElement<InputProps>
  /** Whether the field is readOnly. */
  readOnly?: boolean
  /** The role of this form field */
  role?: 'group' | 'radiogroup'
}

/**
 * The FormField provides a consistent layout for form inputs, including a label, description, and error message.
 *
 * It wires the `label`, `description`, and `error` props to the appropriate elements and provides accessibility attributes to the child component, using an autogenerated id.
 * If an id is provided to the child component, it will be used instead.
 *
 **/
const FormField = ({
  label,
  labelScreenreaderOnly,
  description,
  error,
  warning,
  children,
  readOnly,
  role,
}: Props) => {
  const generatedId = useId().replaceAll(':', '_')
  const { id: providedId } = children.props

  const fieldset = role === 'group' || role === 'radiogroup'

  const id = providedId ?? generatedId

  const descriptionId = description && `description-${id}`
  const errorId = error && `error-${id}`
  const warningId = warning && `warning-${id}`

  const descriptors = [descriptionId, errorId].filter((s): s is string => !!s)

  const required = error === 'Required'

  const info = (
    <>
      {!required && error && (
        <p className="input-error" id={errorId}>
          <ExclamationCircle aria-hidden className="mr-xs mt-xs" />
          Error: {error}
        </p>
      )}
      {warning && (
        <p className="input-warning" id={warningId}>
          <ExclamationCircle aria-hidden className="mr-xs mt-xs h-md w-md" />
          {warning}
        </p>
      )}

      {description && (
        <p className="input-description" id={descriptionId}>
          {description}
        </p>
      )}
    </>
  )

  const Container = fieldset ? 'fieldset' : 'div'
  const Label = fieldset ? 'legend' : 'label'

  const ariaProps = {
    'aria-describedby': descriptors.join(' ') || undefined,
    'aria-invalid': error ? true : undefined,
    'aria-label': label,
  }

  return (
    <Container
      role={role}
      className="form-field"
      data-readonly={readOnly}
      data-required={required}
      data-fieldset={fieldset}
      {...(fieldset && ariaProps)}
    >
      <Label
        className={clsx('input-label', { 'sr-only': labelScreenreaderOnly })}
        {...(!fieldset && { htmlFor: id })}
      >
        {label}
      </Label>
      {readOnly && info}

      <div className="input">
        {cloneElement(children, {
          id,
          readOnly,
          ...(!fieldset && ariaProps),
        })}
      </div>
      {!readOnly && required && (
        <span className="input-required">
          <Asterisk className="required-icon" aria-hidden />
          Required
        </span>
      )}
      {!readOnly && info}
    </Container>
  )
}

export { FormField }

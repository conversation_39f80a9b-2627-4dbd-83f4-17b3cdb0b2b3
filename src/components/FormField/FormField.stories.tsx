import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'
import { FormField } from '.'
import { NumberInput } from '../NumberInput'

const meta: Meta<typeof FormField> = {
  title: 'Components/FormField',
  component: FormField,
  parameters: {
    layout: 'centered',
  },
}

export default meta

type Story = StoryObj<typeof FormField>

export const Default: Story = {
  args: {
    label: 'Example Label',
    description: 'This is a description for the form field.',
    error: 'This is an error message.',
    warning: 'This is a warning message.',
    children: <input type="text" placeholder="Some Placeholder Text" />,
  },
}

export const DisabledInput: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Example of a form field with an input that is disabled.',
      },
    },
  },
  args: {
    label: 'Example Label',
    children: (
      <input type="text" placeholder="Some Placeholder Text" disabled />
    ),
  },
}

export const Examples: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Examples of the `FormField` used with different input types.',
      },
    },
  },
  argTypes: { children: { control: { disable: true } } },
  args: {
    label: 'Example Label',
    description: 'This is a description for the form field.',
    error: 'This is an error message.',
  },

  render: (args) => {
    const inputs = [
      <NumberInput key={1} step={0.1} format="percent" initialValue={0} />,
    ]

    return (
      <div className="space-y-md">
        {inputs.map((input, index) => (
          <FormField key={index} {...args}>
            {input}
          </FormField>
        ))}
      </div>
    )
  },
}

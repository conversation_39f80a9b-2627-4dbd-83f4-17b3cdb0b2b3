import { <PERSON><PERSON>vent<PERSON><PERSON><PERSON>, useId, useRef, useState } from 'react'
import { Button, ButtonProps, Overlay, Tooltip } from 'react-bootstrap'
import { InfoCircle } from 'react-bootstrap-icons'

type Props = ButtonProps & {
  /** The text to display in the tooltip  */
  text: string
  /** The label for the button */
  label: string
  /**  The click handler for the button */
  onClick: MouseEventHandler<HTMLButtonElement>
  /**  The disabled state of the button */
  disabled?: boolean
}

const Toggletip = (props: Props) => {
  const [show, setShow] = useState(false)
  const target = useRef(null)
  const tooltipId = useId()

  return (
    <>
      <Button
        aria-label={props.label}
        id={tooltipId}
        {...props}
        ref={target}
        type="button"
        onClick={() => setShow(!show)}
        className="btn-toggletip"
        variant=""
      >
        <InfoCircle role="presentation" className="icon" />
      </Button>
      <Overlay
        target={target.current}
        show={show}
        placement="top"
        rootClose
        onHide={() => setShow(false)}
      >
        <Tooltip aria-describedby={tooltipId} role="status">
          {props.text}
        </Tooltip>
      </Overlay>
    </>
  )
}

export { Toggletip }

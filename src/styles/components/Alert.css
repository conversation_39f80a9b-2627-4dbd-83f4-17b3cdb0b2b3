/**
* Base Component Definition
*/

.alert {
  --gatx-alert-border-color: unset;
  --gatx-alert-bg-color-base: unset;
  --gatx-alert-bg-color-alpha: unset;
  --gatx-alert-text-color: unset;
  --gatx-alert-body-text-color: unset;
  --gatx-alert-heading-after: unset;

  --gatx-alert-body-display: block;
  --gatx-alert-flex-direction: row-reverse;
  --gatx-alert-align-items: start;
  --gatx-alert-body-margin-top: var(--gatx-spacing-xs);

  --gatx-alert-icon-size: 1rem;

  --gatx-alert-close-button-size: 1.5rem;

  display: flex;
  flex-direction: var(--gatx-alert-flex-direction);
  align-items: var(--gatx-alert-align-items);

  padding: var(--gatx-spacing-md);
  gap: var(--gatx-spacing-sm);
  border-width: 1px;
  border-radius: var(--gatx-border-radius);
  border-color: var(--gatx-alert-border-color);
  background-color: color-mix(
    in srgb,
    var(--gatx-alert-bg-color-base),
    transparent var(--gatx-alert-bg-color-alpha)
  );
  color: var(--gatx-alert-text-color);

  .icon {
    align-self: start;
    width: var(--gatx-alert-icon-size);
    height: var(--gatx-alert-icon-size);
    color: var(--gatx-alert-border-color);
  }

  .btn-close {
    width: var(--gatx-alert-close-button-size);
    height: var(--gatx-alert-close-button-size);
  }
}

.alert-heading {
  display: inline;

  &::after {
    padding: 0 0.2rem;
    content: var(--gatx-alert-heading-after);
  }
}

.alert-body {
  display: var(--gatx-alert-body-display);
  margin-top: var(--gatx-alert-body-margin-top);
  color: var(--gatx-alert-body-text-color);
}

.alert-content {
  margin-top: -0.25rem;
}

/**
* Variants - Level
*/

.alert-error {
  --gatx-alert-border-color: var(--gatx-colors-error-5);
  --gatx-alert-bg-color-base: var(--gatx-colors-error-1);
  --gatx-alert-bg-color-alpha: 50%;
  --gatx-alert-text-color: var(--gatx-typography-color-text);
  --gatx-alert-body-text-color: var(--gatx-colors-dark-5);

  @media (prefers-color-scheme: dark) {
    --gatx-alert-border-color: var(--gatx-colors-error-4);
    --gatx-alert-bg-color-base: var(--gatx-colors-error-7);
    --gatx-alert-bg-color-alpha: 70%;
    --gatx-alert-text-color: var(--gatx-typography-color-text);
    --gatx-alert-body-text-color: var(--gatx-colors-dark-2);
  }
}

.alert-warning {
  --gatx-alert-border-color: var(--gatx-colors-warning-5);
  --gatx-alert-bg-color-base: var(--gatx-colors-warning-1);
  --gatx-alert-bg-color-alpha: 50%;
  --gatx-alert-text-color: var(--gatx-typography-color-text);
  --gatx-alert-body-text-color: var(--gatx-colors-dark-5);

  @media (prefers-color-scheme: dark) {
    --gatx-alert-bg-color-base: var(--gatx-colors-warning-8);
    --gatx-alert-bg-color-alpha: 70%;
    --gatx-alert-text-color: var(--gatx-typography-color-text);
    --gatx-alert-body-text-color: var(--gatx-colors-dark-2);
  }
}

.alert-info {
  --gatx-alert-border-color: var(--gatx-colors-secondary-5);
  --gatx-alert-bg-color-base: var(--gatx-colors-secondary-2);
  --gatx-alert-bg-color-alpha: 70%;
  --gatx-alert-text-color: var(--gatx-typography-color-text);
  --gatx-alert-body-text-color: var(--gatx-colors-dark-5);

  @media (prefers-color-scheme: dark) {
    --gatx-alert-bg-color-base: var(--gatx-colors-secondary-9);
    --gatx-alert-bg-color-alpha: 70%;
    --gatx-alert-text-color: var(--gatx-typography-color-text);
    --gatx-alert-body-text-color: var(--gatx-colors-dark-2);
  }
}

.alert-success {
  --gatx-alert-border-color: var(--gatx-colors-success-5);
  --gatx-alert-bg-color-base: var(--gatx-colors-success-1);
  --gatx-alert-bg-color-alpha: 50%;
  --gatx-alert-text-color: var(--gatx-typography-color-text);
  --gatx-alert-body-text-color: var(--gatx-colors-dark-5);

  @media (prefers-color-scheme: dark) {
    --gatx-alert-bg-color-base: var(--gatx-colors-success-8);
    --gatx-alert-bg-color-alpha: 70%;
    --gatx-alert-text-color: var(--gatx-typography-color-text);
    --gatx-alert-body-text-color: var(--gatx-colors-dark-2);
  }
}

/**
* Variants - Layout
*/

.alert-compact {
  --gatx-alert-body-display: inline;
  --gatx-alert-flex-direction: row;
  --gatx-alert-align-items: center;
  --gatx-alert-heading-after: '—';
}

/**
* Safety and Quality Alert
*/

.safety-quality-alert li:not(:last-child)::after {
  padding: 0 16px;
  content: '|';
}

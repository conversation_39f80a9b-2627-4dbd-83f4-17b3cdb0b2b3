/**
* Base Component Definition
*/

.btn-toggletip.btn-primary.btn {
  --gatx-btn-bg: transparent;
  --gatx-btn-border-color: none;
  --gatx-btn-color: var(--gatx-colors-light-9);
  --gatx-btn-border-radius: 50%;
  --gatx-btn-padding: 0;
  --gatx-btn-font-size: 1rem;

  width: 1rem;
  height: 1rem;
  margin: 0;
  border: none;
  background-color: var(--gatx-btn-bg);

  @media (prefers-color-scheme: dark) {
    --gatx-btn-color: var(--gatx-colors-dark-3);
  }

  .icon {
    width: 1rem;
    height: 1rem;
    margin: 0;
    padding: 0;
    color: var(--gatx-btn-color);
  }

  &:hover {
    --gatx-btn-color: var(--gatx-colors-dark-8);

    @media (prefers-color-scheme: dark) {
      --gatx-btn-color: var(--gatx-colors-light-1);
    }
  }

  &:focus {
    --gatx-btn-color: var(--gatx-colors-dark-8);
    outline: none;

    @media (prefers-color-scheme: dark) {
      --gatx-btn-color: var(--gatx-colors-light-1);
    }
  }
}

/**
* Tooltip Component Styling
*/

.tooltip {
  --gatx-tooltip-bg: var(--gatx-colors-primary-10);
  --gatx-tooltip-color: var(--gatx-colors-light-1);
  --gatx-tooltip-border-radius: var(--gatx-border-radius);
  --gatx-tooltip-padding: var(--gatx-spacing-xs) var(--gatx-spacing-sm);
  --gatx-tooltip-font-size: var(--gatx-typography-font-size-sm);
  --gatx-tooltip-max-width: 400px;
  --gatx-tooltip-z-index: 1070;

  z-index: var(--gatx-tooltip-z-index);

  position: absolute;

  font-weight: var(--gatx-typography-font-weight-regular);
  font-size: var(--gatx-tooltip-font-size);
  line-height: var(--gatx-typography-line-height);
  font-family: var(--gatx-typography-font-family);

  @media (prefers-color-scheme: dark) {
    --gatx-tooltip-bg: var(--gatx-colors-light-1);
    --gatx-tooltip-color: var(--gatx-colors-dark-8);
  }
}

.tooltip-inner {
  max-width: var(--gatx-tooltip-max-width);
  padding: var(--gatx-tooltip-padding);
  border-radius: var(--gatx-tooltip-border-radius);
  background-color: var(--gatx-tooltip-bg);
  color: var(--gatx-tooltip-color);
}

.tooltip-arrow {
  display: block;
  position: absolute;
  width: 0.75rem;
  height: 0.375rem;

  &::before {
    position: absolute;
    border-color: transparent;
    content: '';
  }
}

/* Tooltip positioning and arrow styles */
.bs-tooltip-top {
  padding: var(--gatx-spacing-xs) 0;

  .tooltip-arrow {
    bottom: 0;

    &::before {
      top: 2px;
      border-width: 0.4rem 0.4rem 0;
      border-top-color: var(--gatx-tooltip-bg);
    }
  }
}

import { useId, useState } from 'react'
import {
  Dropdown as Bootstra<PERSON><PERSON>ropdown,
  DropdownToggleProps,
} from 'react-bootstrap'
import * as Icons from 'react-bootstrap-icons'
import { AlignType } from 'react-bootstrap/esm/types'
import { Item } from './DropDownItem'

const POPPER_CONFIG = {
  modifiers: [
    {
      name: 'eventListeners',
      options: {
        scroll: true,
        resize: true,
      },
    },
    {
      name: 'effectModifier',
      enabled: true,
      // This is to prevent type error for this propery
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      phase: 'main' as any,
      effect: () => {
        return () => null
      },
    },
    {
      name: 'preventOverflow',
      options: {
        boundary: 'viewport',
      },
    },
    {
      name: 'computeStyles',
      options: {
        adaptive: true,
      },
    },
  ],
}

type Toggle = DropdownToggleProps['as']

type Props = {
  /** Items in the dropdown menu */
  children: React.ReactNode
  /** The label of the dropdown toggle button */
  label: string
  /** Whether the label is only accessible to the screen reader*/
  labelScreenreaderOnly?: boolean
  /** Whether to display a chevron icon next to the dropdown header */
  noChevron?: boolean
  /** A custom toggle component to be used. Must forward a reference to its inner button. */
  Toggle?: Toggle
  /** Whether the dropdown is disabled */
  disabled?: boolean
  /** The "align" preference for the drop-down */
  dropAlign?: AlignType
}
/**
 * The DropDown component is used to provide a menu of navigation links or
 * actions to the user. It should not be used as a form select dropdown (use the
 * Select component for that purpose).
 *
 * All child dropdown items must be defined with either an `href` or `onClick`
 * prop, which will determine the type of item rendered and its behavior. The
 * rendered component can be customized with the `Component` prop, for use cases
 * like dom routing where a different link implementation is needed.
 */
export const ToggleMenu = ({
  children,
  label,
  labelScreenreaderOnly,
  noChevron,
  Toggle,
  disabled = false,
  dropAlign,
}: Props) => {
  const id = useId().replaceAll(':', '_')

  const [isToggled, setIsToggled] = useState(false)
  const Icon = !isToggled ? Icons.ChevronDown : Icons.ChevronUp

  const handleToggle = () => {
    setIsToggled((prev) => !prev)
  }

  return (
    <BootstrapDropdown onToggle={handleToggle} align={dropAlign}>
      <BootstrapDropdown.Toggle
        disabled={disabled}
        as={Toggle}
        id={id}
        aria-label={label}
        aria-haspopup
        aria-controls={`${id}-menu`}
        className={noChevron ? undefined : 'dropdown-display-toggle'}
      >
        {!labelScreenreaderOnly && label}
        {!noChevron && <Icon aria-hidden className="dropdown-icon" />}
      </BootstrapDropdown.Toggle>

      <BootstrapDropdown.Menu
        role="menu"
        id={`${id}-menu`}
        popperConfig={POPPER_CONFIG}
      >
        {children}
      </BootstrapDropdown.Menu>
    </BootstrapDropdown>
  )
}
export const DropDown = { ToggleMenu, Item }
export type { DropDownItem } from './DropDownItem'

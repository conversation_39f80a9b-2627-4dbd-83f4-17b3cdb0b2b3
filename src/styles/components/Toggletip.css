/**
* Base Component Definition
*/

.btn-toggletip.btn {
  --gatx-btn-bg: transparent;
  --gatx-btn-border-color: none;
  --gatx-btn-color: var(--gatx-colors-light-9);
  --gatx-btn-border-radius: 50%;
  --gatx-btn-padding: 0;
  --gatx-btn-font-size: 1rem;

  /* Button dimensions for circular shape */
  width: 1rem;
  height: 1rem;

  @media (prefers-color-scheme: dark) {
    --gatx-btn-color: var(--gatx-colors-dark-2);
  }

  .icon {
    width: 1rem;
    height: 1rem;
    color: var(--gatx-btn-color);
  }

  &:hover {
    color: var(--gatx-colors-dark-8);
  }

  
}

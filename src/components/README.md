# Platform One Component Library

The Platform One design system implements an opinionated set of interactive
components, which are expected to be fully accessible by default, with their
property APIs pared down to support just the variants and use cases considered
under the platform's broader design philosophy.

We adhere to a set of principles in the way we structure our components, which
allows us to abstract away the implementation details of any underlying
libraries, test our implementations in a simple way, and offer a consistent
development experience across the full breadth of components.

## `@/components/<Component>/index.tsx`

Our components follow a consolidated entrypoint or "launchpad" pattern for file
organization, which allows consumers of the library to import the full
definition of the component without having to nest the path further than the
top-level `@/components` directory:

```tsx
import Component from '@/components/<Component>'
```

To support this, every component is ultimately represented as the default export
of an `index.tsx` file within an appropriately-named directory.

### Base component definition

For any component that's not inherently dependent on some sort of state or
context, we can just inline the base component definition in the index file.

For more complicated implementation patterns, where server actions, other types
of network calls, or any state external to the component might be required in
practice, we should define a "dumb" version of the component that holds no
internal state, and simply accepts all of the necessary state values via props,
exported from `@/components/Component/Component.tsx`. The entrypoint file would
then be responsible for importing this base component, loading or creating the
relevant external state, and passing the appropriate properties down to achieve
the desired functionality.

This export access pattern allows us to test the base component in isolation
(see below), while still offering convenience for consumers of the component
library in a real-world application by giving us a natural place to layer in
future rich functionality without complicating the component API.

### Documentation

Component-level documentation should be written in JSDoc format in two places:
(1) immediately above the export of each component/subcomponent, and (2) inline
with the property type declarations for those components.

This documentation should describe the high-level functionality represented by
the component, its overall purpose as a part of the design system, and any
implementation quirks that engineers or designers need to be aware of when
interacting with the component.

Prop documentation should just give a brief summary of what each property
represents, and how to use it.

This documentation is valuable for engineers working directly within the
codebase, and is ingested by Storybook (see below) to populate auto-generated
documentation files.

### Subcomponent composition

If the component being constructed is a _composable_ component, with multiple
separate elements that need to be combined together to achieve the final desired
effect, the index file should export the full suite of subcomponents within the
default export. See the Accordion or Tabs components for examples of this
pattern in action, as it's difficult to represent in a contrived inline example.

## `@/components/<Component>/<Component>.stories.tsx`

A vital element of the implementation of any component for the design system is
exposing that component in Storybook to:

1. Support manual and visual testing of the component,
2. Document the intended use cases and purposes of different variants, and
3. Improve discoverability of the component for new users of the design system.

To that end, every component should have a story file, where we capture stories
for any meaningful and intentional variations in the properties passed to the
base component.

For information about the basic process of writing stories, see [Storybook's
Documentation][1]. We'll describe a few patterns specific to our use of the
library here, however:

```tsx
export const Urgent: Story = {
  name: 'Variant: Urgent',
  parameters: {
    docs: {
      description: {
        story: `This variant is appropriate when we expect the component to be
          featured prominently, and want any changes to the element's content to
          be announced aggressively. Changes will interrupt users accessing the
          site using a screen reader, so use sparingly. For emergency uses
          only!`,
      },
    },
  },
  args: {
    variant: 'urgent',
    content: 'Something wicked this way comes!',
  },
}
```

First, you'll notice that the exported const doesn't include the component name.
Because of the way Storybook structures its interface and the way the story
files are composed, it's helpful to eliminate redundancy and focus on finding a
name that describes _just_ the specific use case represented by the story.

Second, you'll notice that we're overriding the story's name to a more
human-readable format. We want to prioritize the legibility of the final
Storybook, to ensure that our component library is maximally understandable by
non-engineers.

Finally, we should be writing explicit descriptions for each story, explaining
the situations in which the particular combination of values represented by that
story is appropriate according to the platform's design philosophy. Work with
your designer to ensure stories are aligned with realistic use cases! This
process is part of how we determine the right abstractions and opinions for our
component definitions; if we can't explain when and where we'd want to use a
specific value for a specific prop, it probably shouldn't be exposed by our
library's API.

For simpler components without meaningful variants, it's appropriate to export a
single `Default` story, without the need for any explicit naming or descriptions
-- the component's JSDoc documentation should be sufficient in these cases.

## `@/components/<Component>/<Component>.test.tsx`

Each component is expected to implement `jest` specs to test their
implementation for general accessibility and the proper handling of any non-
cosmetic, non-trivial properties or functionality.

Because our components are frequently reliant on CSS rules for their full
functionality (e.g., visually hiding/displaying content based on the component's
state), we use a wrapped version of `@testing-library/react`'s `render` function
that injects the design system's global styles, exported from `@/tests/react`.

For rudimentary accessibility testing, we're using the `jest-axe` library. At a
minimum, any user-facing component should be tested using this tool. An
extremely basic test file for a component that tests for accessibility but has
no meaningful functionality to test would look something like the below:

```tsx
import { render, screen } from '@/tests/react'
import { axe } from 'jest-axe'
import Component from '.'

describe('Component', () => {
  test('adheres to accessibility guidelines', async () => {
    const { container } = render(<Component />)
    expect(await axe(container)).toHaveNoViolations()
  })
})
```

If the component is interactive in some way, we should exercise the component's
implementation using `@testing-library/user-event` to imitate the way a user
is expected to interact with the element:

```tsx
import { render, screen } from '@/tests/react'
import userEvent from '@testing-library/user-event'
import Component from '.'

describe('Component', () => {
  test('handles click events', async () => {
    const onClick = jest.fn()
    const label = 'My Component Instance'

    render(<Component label={label} onClick={onClick} />)
    await userEvent.click(screen.getByText(label))

    expect(onClick).toHaveBeenCalled()
  })

  // accessibility test would go here
})
```

## `@/styles/components/<Component>.css`

Styles for a component live in a dedicated file for that component, with all of
the styling rules for the component, its subcomponents, and various interactive
states living in one place. See [the Component Styling README][2] for more
information about our engineering approach to component styling.

[1]: https://storybook.js.org/docs/writing-stories
[2]: ../styles/components/README.md

import clsx from 'clsx'
import React, { ElementType } from 'react'

type Props = {
  id?: string
  text: string | React.ReactNode
  Icon: ElementType
  onClick?: () => void
  iconOnly?: boolean
}

const ResourceButton = ({
  text,
  Icon,
  onClick,
  iconOnly = false,
  ...props
}: Props) => {
  return (
    <button
      id={props.id}
      onClick={onClick}
      className="resource-sidebar-item flex items-center gap-lg"
      {...props}
    >
      <Icon className="resource-sidebar-icon size-md shrink-0" aria-hidden />
      <span className={clsx('w-full', { 'sr-only': iconOnly })}>{text}</span>
    </button>
  )
}

export { ResourceButton }

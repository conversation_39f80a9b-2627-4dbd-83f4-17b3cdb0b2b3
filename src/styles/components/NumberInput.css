.number-input {
  --gatx-number-input-color: var(--gatx-colors-light-10);
  --gatx-number-input-bg: var(--gatx-colors-light-5);

  --gatx-number-input-btn-color: var(--gatx-colors-light-10);
  --gatx-number-input-btn-bg: var(--gatx-colors-light-5);
  --gatx-number-input-hover-btn-bg: var(--gatx-colors-primary-2);
  --gatx-number-input-focus-border-color: var(--gatx-colors-primary-7);

  --gatx-number-input-disabled-color: var(--gatx-colors-light-5);
  --gatx-number-input-disabled-btn-color: var(--gatx-colors-light-5);

  @media (prefers-color-scheme: dark) {
    --gatx-number-input-color: var(--gatx-colors-dark-1);
    --gatx-number-input-bg: var(--gatx-colors-dark-8);

    --gatx-number-input-btn-color: var(--gatx-colors-dark-1);
    --gatx-number-input-btn-bg: var(--gatx-colors-dark-8);
    --gatx-number-input-hover-btn-bg: var(--gatx-colors-secondary-10);
    --gatx-number-input-focus-border-color: var(--gatx-colors-secondary-5);

    --gatx-number-input-disabled-color: var(--gatx-colors-dark-4);
    --gatx-number-input-disabled-btn-color: var(--gatx-colors-dark-4);
  }
  width: fit-content;
  min-width: 160px;
  padding: var(--gatx-spacing-xs);
  border-radius: var(--gatx-border-radius);

  background: var(--gatx-number-input-bg);

  .wj-input-group {
    display: inline-flex;
  }

  .wj-form-control {
    grow: 2;
    min-width: 88px;
    background: var(--gatx-number-input-bg);
    color: var(--gatx-number-input-color);
    text-align: center;

    &.disabled {
      color: var(--gatx-number-input-disabled-color);
    }
  }

  .wj-btn {
    width: 32px;
    height: 32px;

    &:hover {
      border-radius: 50%;
      background-color: var(--gatx-number-input-hover-btn-bg);
    }

    &:disabled {
      color: var(--gatx-number-input-disabled-btn-color);
    }

    &:disabled:hover {
      background-color: transparent;
    }
  }

  &:has(input:focus) {
    outline: 1px solid var(--gatx-number-input-focus-border-color);

    .wj-form-control {
      border: none;
      outline: none;
    }
  }

  &[disabled] {
    .wj-form-control {
      color: var(--gatx-number-input-disabled-color);
    }

    .wj-btn {
      color: var(--gatx-number-input-disabled-btn-color);

      &:hover {
        background-color: transparent;
        cursor: auto;
      }
    }

    .number-input {
      --gatx-number-input-color: var(--gatx-colors-light-10);
      --gatx-number-input-bg: var(--gatx-colors-light-5);

      --gatx-number-input-btn-color: var(--gatx-colors-light-10);
      --gatx-number-input-btn-bg: var(--gatx-colors-light-5);

      --gatx-number-input-disabled-color: var(--gatx-colors-light-5);
      --gatx-number-input-disabled-btn-color: var(--gatx-colors-light-5);

      @media (prefers-color-scheme: dark) {
        --gatx-number-input-color: var(--gatx-colors-dark-1);
        --gatx-number-input-bg: var(--gatx-colors-dark-8);

        --gatx-number-input-btn-color: var(--gatx-colors-dark-1);
        --gatx-number-input-btn-bg: var(--gatx-colors-dark-8);

        --gatx-number-input-disabled-color: var(--gatx-colors-dark-4);
        --gatx-number-input-disabled-btn-color: var(--gatx-colors-dark-4);
      }

      min-width: 160px;

      .wj-form-control {
        background: var(--gatx-number-input-bg);
        color: var(--gatx-number-input-color);
        text-align: center;

        &.disabled {
          color: var(--gatx-number-input-disabled-color);
        }
      }

      .wj-btn:enabled {
        background: var(--gatx-number-input-btn-bg);
        color: var(--gatx-number-input-btn-color);
      }

      .wj-btn:disabled {
        color: var(--gatx-number-input-disabled-btn-color);
      }
    }
  }
}

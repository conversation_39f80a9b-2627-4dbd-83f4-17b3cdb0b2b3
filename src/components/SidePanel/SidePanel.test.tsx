import { render, screen } from '@/tests/react'
import userEvent from '@testing-library/user-event'
import { axe } from 'jest-axe'
import { SidePanel } from '.'

describe('SidePanel', () => {
  const handleClose = jest.fn()

  afterEach(() => {
    jest.clearAllMocks()
  })

  test('triggers onClose when the close button is clicked', async () => {
    const handleClose = jest.fn()

    render(
      <SidePanel title="Car Details" onClose={handleClose}>
        Main Content
      </SidePanel>,
    )

    await userEvent.click(
      screen.getByRole('button', { name: 'Close Side Panel' }),
    )

    expect(handleClose).toHaveBeenCalled()
  })

  test('renders the resizable side panel with a default width of 1/3', () => {
    const handleResize = jest.fn()

    render(
      <SidePanel title="Car Details" onResize={handleResize}>
        Main Content
      </SidePanel>,
    )

    expect(
      screen.getByRole('radio', { name: 'One Third Screen' }),
    ).toBeChecked()
  })

  test.each([
    { size: 'full', label: 'Full Screen (Hides Main Content)' },
    { size: 'half', label: 'Half Screen' },
  ])(
    'triggers onResize when the %s size input is clicked',
    async ({ size, label }) => {
      const handleResize = jest.fn()

      render(
        <SidePanel title="Car Details" onResize={handleResize}>
          Main Content
        </SidePanel>,
      )

      await userEvent.click(screen.getByRole('radio', { name: label }))

      expect(handleResize).toHaveBeenCalledWith(size)
    },
  )

  test('adheres to accessibility guidelines', async () => {
    const { container } = render(
      <SidePanel title="Car Details" onClose={handleClose}>
        Main Content
      </SidePanel>,
    )
    expect(await axe(container)).toHaveNoViolations()
  })
})

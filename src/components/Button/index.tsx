import { forwardRef } from 'react'
import { Button as Bootstrap<PERSON><PERSON>on, Button<PERSON>rops } from 'react-bootstrap'
import * as Icons from 'react-bootstrap-icons'

const SIZE_MAPPINGS = { small: 'sm', medium: undefined, large: 'lg' } as const

type Size = keyof typeof SIZE_MAPPINGS
// Icon names available for use in icon buttons
type IconName = keyof typeof Icons
// Base props shared between all button types
export type Props = Omit<ButtonProps, 'size' | 'as'> & {
  /** The button's text. */
  text: string
  /** The specific style of button to render. See below for supported styles and their intended uses. */
  variant?: 'primary' | 'outline' | 'text' | 'neutral'
  /** The size of the button. See below for appropriate use cases for non-default alternate sizes. */
  size?: Size
  /** The icon to render within the button, if any.
   * Access https://icons.getbootstrap.com/ to see the list of icons.
   * The icon name should be passed as a string, using PascalCase convention e.g. 'PlusSquare' or 'ArrowLeftShort'.
   */
  icon?: IconName
  /** Whether or not the button should be disabled for interaction. */
  disabled?: boolean
}

/**
 * The Button component is used any place we want to allow users to trigger a
 * specific interaction.
 */
const Button = forwardRef<HTMLButtonElement, Props>(function Button(
  {
    text,
    variant = 'primary',
    size = 'medium',
    icon,
    disabled = false,
    ...props
  },
  ref,
) {
  // Defines the icon component to render, if any
  const IconComponent = icon ? Icons[icon] : null
  return (
    <BootstrapButton
      ref={ref}
      type="button"
      {...props}
      variant={variant}
      size={SIZE_MAPPINGS[size]}
      disabled={disabled}
    >
      {IconComponent && <IconComponent aria-hidden className="icon" />}
      {text}
    </BootstrapButton>
  )
})

export { Button, type Props as ButtonProps, type IconName }

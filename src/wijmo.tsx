'use client'

import { setLicenseKey } from '@mescius/wijmo'
import { PropsWithChildren } from 'react'

type Props = PropsWithChildren<{
  license: string
}>

/**
 * WijmoLicenseProvider just wraps other elements to ensure that the Wijmo
 * license key is set before any scripts that might actually consume the library
 * get invoked. Assuming a valid license key has been set in the relevant ENV
 * variable, this will suppress Wijmo's popups and other intrusive UI elements.
 *
 * Because this ENV variable is replaced with a static value at build time, we
 * need the Wijmo license key injected during compile time to be valid for any
 * domains where this application will be running. Ideally, we provide a
 * distribution key valid for a wildcard domain, which would make the key
 * consistent with our one-TLD session cookie strategy for authentication.
 */
export const WijmoLicenseProvider = ({ children, license }: Props) => {
  setLicenseKey(license)

  return children
}

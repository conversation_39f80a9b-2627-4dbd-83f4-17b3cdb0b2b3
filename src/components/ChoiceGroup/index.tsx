import { useId } from 'react'
import { ChoiceControl, Size } from '../ChoiceControl'
import { FormField } from '../FormField'
import { ReadOnlyInput } from '../ReadOnlyInput'

type MultiSelect<T> = {
  /** Whether the chip group allows for multiple values to be selected. */
  multiSelect: true
  /** The selected value(s) for the group of chips. */
  value?: T[]
  /** The function called when the selected values change. */
  onChange: (value: T[]) => void
}

type SingleSelect<T> = {
  /** Whether the chip group allows for multiple values to be selected. */
  multiSelect: false
  /** The selected value(s) for the group of chips. */
  value?: T
  /** The function called when the selected values change. */
  onChange: (value: T) => void
}

type Option<T> = { label: string; ariaLabel?: string; value: T }

type BaseProps<T> = {
  /** The label text for the group of choices. */
  label: string
  /** Whether the legend should be visually hidden. */
  labelScreenreaderOnly?: boolean
  /** The description or helper text for the groups of choices. */
  description?: string
  /** The error message text for the group of choices. */
  error?: string
  /** The warning message text for the group of choices. */
  warning?: string
  /** Whether the choice group and choice are disabled. When true, the control cannot be interacted with. */
  disabled?: boolean
  /** The options for the group of controls. Each option must have a label and value. */
  options: Option<T>[]
  /** Additional ChoiceControl Props: The size of the chip. */
  size?: Size
  /** Whether the input is readOnly */
  readOnly?: boolean
  /** `classic`: standard checkbox / radio button | `chip`: chip version */
  variant?: 'classic' | 'chip'
  /** Layout options to dispaly the list of choices */
  layout?: 'vertical' | 'inline'
}

type Props<T> = BaseProps<T> & (MultiSelect<T> | SingleSelect<T>)

/**
 * ChoiceGroup is a collection of controls that includes support for a label, description, and error message.
 *
 *  The component includes the constructing of the `ChoiceControl` component and managing its selected state.
 **/
const ChoiceGroup = <T,>({
  label,
  labelScreenreaderOnly,
  description,
  error,
  warning,
  options,
  disabled = false,
  multiSelect,
  value,
  onChange,
  readOnly,
  variant = 'chip',
  layout = 'inline',
  ...additionalChipProps
}: Props<T>) => {
  const id = useId().replaceAll(':', '_')

  const selectedValues = [value].flat().filter(Boolean) as T[]

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (multiSelect) {
      if (event.target.checked) {
        onChange([...selectedValues, event.target.value as T])
      } else {
        onChange(selectedValues.filter((item) => item !== event.target.value))
      }
    } else {
      onChange(event.target.value as T)
    }
  }

  const required = error === 'Required'

  function isSelected(option: Option<T>) {
    return selectedValues.some((v) => v === option.value)
  }

  return (
    <FormField
      label={label}
      labelScreenreaderOnly={labelScreenreaderOnly}
      description={description}
      error={error}
      warning={warning}
      readOnly={readOnly}
      role={multiSelect ? 'group' : 'radiogroup'}
    >
      {readOnly ? (
        <ReadOnlyInput
          value={selectedValues
            .map((val) => options.find((option) => option.value === val)?.label)
            .join(', ')}
        />
      ) : (
        <div
          className="choice-group"
          data-layout={layout}
          data-required={required}
          data-readonly={readOnly}
        >
          {options.map((option) => (
            <ChoiceControl
              {...additionalChipProps}
              variant={variant}
              isSelected={isSelected(option)}
              key={String(option.value)}
              label={option.label}
              value={option.value as string}
              type={multiSelect ? 'checkbox' : 'radio'}
              disabled={disabled}
              name={id}
              onChange={handleChange}
              aria-label={option.ariaLabel ?? option.label}
            />
          ))}
        </div>
      )}
    </FormField>
  )
}

export { ChoiceGroup }
export type { Option }

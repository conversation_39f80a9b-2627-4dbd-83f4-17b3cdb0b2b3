# Component CSS

The styles for components in our design system should be self-contained, and
adhere to some standard patterns. This README is an attempt to document those
patterns and explain the rationale behind them.

We're using Bootstrap as the foundation for our design system, but rather than
including their full styles, we're using a heavily modified version of their CSS
that gives us more control over the lifecycle of our variables and the different
variants we'd like to introduce.

That said, [Bootstrap's SCSS files][1] are a good starting place for defining
most of our components, and usually gives us a fair amount of the base component
definition to work off of. To get us the rest of the way there, we'll be
consulting the [component designs in Figma][2], which have references to the
color variables we'll be using and include contextual information about how each
component should show up in the interface.

## Base Component Definition

The base component definition should contain all of the classes and styles
necessary for the component to function as intended. This means high-level
styling concerns like a child component being visible/invisible should likely be
coded as static properties.

Any properties that might change between variants or color schemes of the
component should be extracted into namespaced variables that are declared at the
top of the component's root class, in the format `--gatx-<component>-<property>`.
The values of these variables in the root class should be one of the following:

- A common value to be applied for most variants, even if it may be overwritten by some of them.
- A "no-op" value, typically `unset`, when the variable is not relevant for most variants.

Variables should be ordered according to SMACSS' semantic order, and variables
targeted at any pseudo-classes (`:active`, `:hover`, etc.) should use the format
`--gatx-<component>-<state>-<property>`.

## Variant Definitions

For each supported variation of a component (style, size, etc.), we should have
a block of modifier classes expressing those variants, with each class
overriding any pertinent variables. We should avoid introducing one-off
CSS rules within these variants, to ensure that we maintain the ability for
any underlying variables to be further modified by other classes, etc.

Note that if the component has a "default" variant, you may end up with an
empty variant class rule, indicating that the variant just uses the styles from
the base component definition.

## Example

An example CSS file for a simple pill component might look like this:

```css
/**
* Base component definition
*/
.component {
  --gatx-component-bg: unset;
  --gatx-component-color: unset;
  --gatx-component-font-size: 1rem;

  padding: 0.2rem;
  border-radius: var(--gatx-border-radius);
  color: var(--gatx-component-color);
  font-size: var(--gatx-component-font-size);
}

/**
* Variants - Style
*/
.component-primary {
  --gatx-component-bg: var(--gatx-colors-primary-7);
  --gatx-component-color: var(--gatx-colors-light-1);
}

.component-secondary {
  --gatx-component-bg: var(--gatx-colors-secondary-1);
  --gatx-component-color: var(--gatx-colors-dark-5);
}

/**
* Variants - Size
*/
.component-small {
  --gatx-component-font-size: 0.8rem;
}

.component-large {
  --gatx-component-font-size: 1.2rem;
}
```

For examples of these principles in action in a more complex context, you should
take a look at the CSS files already in this directory.

## Why not use CSS-in-JS?

CSS-in-JS solutions typically introduce performance overhead at runtime, and
require team members to become familiar with whatever paradigm is exposed by the
styling library _in addition to_ understanding actual CSS. It's the opinion of
this author that these libraries are a solution in search of a problem, and
offer very few meaningful advantages over global CSS or CSS modules.

## Why not use CSS modules?

In contrast to CSS-in-JS libraries, CSS modules are an _excellent_ tool for
isolating styling in a scalable, maintainable way, but aren't quite appropriate
for our use case. The primary obstacle for our design system is that the
underlying libraries we're building on top of have poor support for applying
class names conditionally, or forwarding class names to subcomponents. The hope
is that by practicing good styling hygiene and maintaining our styling rules in
the manner described in this writeup, we leave the door open for a potential
move to more hand-written component definitions that can properly utilize CSS
modules.

[1]: https://github.com/twbs/bootstrap/tree/main/scss
[2]: https://www.figma.com/design/MFfOmdBNfU9594td8ynDMS/GATX---Bootstrap-5-Design-System?node-id=0-1&node-type=canvas&t=q7mjbspmNQQgC0Tt-0

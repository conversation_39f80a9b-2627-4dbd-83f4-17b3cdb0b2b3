import type { <PERSON>a, StoryObj } from '@storybook/react'
import { fn } from '@storybook/test'
import clsx from 'clsx'
import { useState } from 'storybook/internal/preview-api'
import { SidePanel, SidePanelSize } from '.'
import { Button } from '../Button'
import { Tabs } from '../Tabs'

const meta: Meta<typeof SidePanel> = {
  title: 'Components/SidePanel',
  component: SidePanel,
  parameters: {
    layout: 'padded',
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    title: 'Car Details',
    children: 'Here we show the car details',
    size: 'third',
  },
  argTypes: {
    onClose: {
      control: { type: 'radio' },
      options: ['undefined', 'callback'],
      mapping: { none: undefined, callback: fn() },
    },

    onResize: {
      control: { type: 'radio' },
      options: ['undefined', 'callback'],
      mapping: { none: undefined, callback: fn() },
    },
  },
}

export const Resizable: Story = {
  parameters: {
    docs: {
      description: {
        story: `Resize controls can be added to the side panel by passing the \`onResize\` callback as a prop.
        The actual size change is layout-dependent, so it's controlled by the caller.
        This means this component will only notify the caller of the resize event, but won't actually resize itself.`,
      },
    },
  },
  argTypes: {
    title: { control: { disable: true } },
    children: { control: { disable: true } },
    size: { control: { disable: true } },
  },
  args: {
    title: 'Car Details',
    onResize: fn(),
    children: 'Here we show the car details',
  },
}

export const Closable: Story = {
  parameters: {
    docs: {
      description: {
        story: `Close controls can be added to the side panel by passing the \`onClose\` callback as a prop.
        The actual close action is layout-dependent, so it's controlled by the caller.
        This means this component will only notify the caller of the close event, but won't actually close itself.`,
      },
    },
  },
  argTypes: {
    title: { control: { disable: true } },
    children: { control: { disable: true } },
  },
  args: {
    title: 'Car Details',
    onClose: fn(),
    children: 'Here we show the car details',
  },
}

export const Tabbed: Story = {
  parameters: {
    docs: {
      description: {
        story: `Tabbed layout can be achieved automatically by passing \`Tabs\` with \`underline\` variant as first child.
        This does not apply to other tab variants.
        When using tabs, the caller should avoid using text nodes as children, as they will generate style inconsistencies. Instead, use a container element like \`div\` or \`span\`.`,
      },
    },
  },
  argTypes: {
    title: { control: { disable: true } },
    children: { control: { disable: true } },
  },
  args: {
    title: 'Car Details',
    children: (
      <Tabs.Container id="sidebar-tabs" variant="underline">
        <Tabs.Pane title="Instructions">
          Here we show the instructions
        </Tabs.Pane>
        <Tabs.Pane title="Car Specifications">
          Here we show the car specification.
        </Tabs.Pane>
        <Tabs.Pane title="Documents">Here we show related documents.</Tabs.Pane>
      </Tabs.Container>
    ),
  },
}

export const Layout: Story = {
  name: 'Example in Layout',
  parameters: {
    docs: {
      description: {
        story: `This is an example of how the \`SidePanel\` could be composed in a two-column layout, taking advantage of the provided callbacks.`,
      },
    },
  },
  render: function Render() {
    const [open, setOpen] = useState(true)
    const [size, setSize] = useState<SidePanelSize>('third')

    return (
      <div
        className={clsx(
          'grid h-screen',
          size === 'half' ? 'grid-cols-2' : 'grid-cols-3',
        )}
      >
        <main
          className={clsx(
            'border border-dashed p-md space-y-md min-h-0',
            open
              ? size === 'half'
                ? 'col-span-1'
                : 'col-span-2'
              : 'col-span-3',
          )}
          hidden={size === 'full'}
        >
          Main Content
          {!open && (
            <Button
              text="Reopen SidePanel"
              onClick={() => setOpen(true)}
              variant="text"
            />
          )}
        </main>
        {open && (
          <div
            className={clsx(
              'min-h-0',
              size === 'full' ? 'col-span-3' : 'col-span-1',
            )}
          >
            <SidePanel
              title="Car Details"
              onClose={() => setOpen(false)}
              onResize={setSize}
              size={size}
            >
              <Tabs.Container id="side-panel-tabs" variant="underline">
                <Tabs.Pane title="Instructions">
                  Here we show the instructions. We can nest tabs in the side
                  panel:
                  <div className="mt-4">
                    <Tabs.Container id="side-panel-subtabs" variant="pills">
                      <Tabs.Pane title="Cleaning">
                        How to clean the car
                      </Tabs.Pane>
                      <Tabs.Pane title="Maintenance">
                        How to maintain the car
                      </Tabs.Pane>
                    </Tabs.Container>
                  </div>
                </Tabs.Pane>
                <Tabs.Pane title="Car Specifications">
                  Here we show the car specification.
                </Tabs.Pane>
                <Tabs.Pane title="Documents">
                  <header className="text-title-sm">
                    Here we show related documents. This tab has really long
                    content, so the side panel will scroll.
                  </header>
                  <p>
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                    Donec eget nisi placerat, dictum massa non, semper risus.
                    Fusce consequat egestas venenatis. Mauris placerat vehicula
                    lectus in auctor. Morbi ultricies et leo eget suscipit. Nunc
                    id malesuada nunc. Sed sed eleifend leo. Integer dui diam,
                    laoreet et tempus ac, ornare eu ante. Nullam cursus, mauris
                    vel viverra scelerisque, justo sem congue eros, ac vulputate
                    libero elit vel purus.
                  </p>
                  <p>
                    Integer viverra ultricies tellus, quis facilisis erat tempus
                    ullamcorper. Fusce rhoncus velit sit amet eros pulvinar, in
                    rhoncus felis convallis. Mauris vel blandit libero. Aliquam
                    eget sodales magna. Phasellus blandit eleifend nisl, id
                    scelerisque diam tempus id. Morbi vitae magna sit amet
                    mauris scelerisque malesuada eget eget mi. Pellentesque
                    faucibus sollicitudin sapien, eget aliquam magna tincidunt
                    vel. Duis pellentesque dictum cursus. Etiam elit ipsum,
                    facilisis in cursus ut, pharetra at est. Ut mattis, diam a
                    porta viverra, orci eros varius tellus, nec molestie orci
                    tellus a risus. Nunc sit amet rhoncus mauris, in malesuada
                    lectus.
                  </p>
                  <p>
                    Donec dictum, libero at condimentum tincidunt, magna neque
                    pharetra risus, ac facilisis orci arcu ullamcorper felis.
                    Etiam quam sem, iaculis a nunc sed, blandit egestas lacus.
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut
                    nisi diam, convallis et ex eu, laoreet vulputate turpis.
                    Phasellus mattis massa mollis ex vulputate finibus.
                    Vestibulum vehicula vestibulum consectetur. Lorem ipsum
                    dolor sit amet, consectetur adipiscing elit. Aenean viverra
                    tellus id leo tristique egestas. Fusce eu commodo magna, nec
                    malesuada purus. Vestibulum commodo leo sit amet leo
                    convallis, nec viverra orci consequat. Cras vel libero odio.
                    Suspendisse urna nisi, fermentum eu tortor in, egestas
                    vehicula eros. Nulla quis nunc vel quam hendrerit
                    consectetur a sed arcu. Praesent orci eros, tincidunt tempor
                    erat eu, pretium blandit ipsum. Duis sit amet nulla maximus
                    est porta commodo efficitur vel augue. Maecenas ipsum felis,
                    porta et pharetra ac, vehicula quis nulla.
                  </p>
                  <p>
                    Morbi a purus in ante venenatis ultricies in sit amet massa.
                    Nullam quis porttitor risus. Curabitur mollis a velit id
                    condimentum. Suspendisse tempus mi dolor, non facilisis mi
                    luctus nec. Nam semper libero in commodo mollis. Quisque id
                    ex varius, volutpat neque a, tristique nisl. Nam egestas
                    semper nibh, id ullamcorper lorem ornare nec. Vivamus massa
                    urna, porta a pellentesque eget, porttitor sed lorem.
                    Integer et erat sed diam aliquam fermentum at eget augue. Ut
                    et placerat libero. Donec viverra est quis mi malesuada, et
                    iaculis tortor dapibus. Nam suscipit diam efficitur,
                    vulputate augue a, vehicula nunc. Vestibulum dapibus ante
                    sed metus euismod ultricies. Maecenas congue augue ultrices
                    vestibulum vehicula. Fusce a lacinia nisl, ut cursus dui.
                  </p>
                  <p>
                    Curabitur aliquam, urna molestie convallis varius, dolor
                    neque condimentum nibh, id maximus felis enim ut tellus.
                    Praesent feugiat est mauris, ut tristique nisl pulvinar
                    gravida. Vivamus auctor ac eros sed tristique. Morbi dapibus
                    velit quis lorem mattis, luctus ultricies justo suscipit.
                    Praesent molestie ante purus, ut egestas quam pellentesque
                    in. Integer arcu turpis, vulputate eu felis tincidunt,
                    eleifend pulvinar felis. Pellentesque dictum eget massa eu
                    tincidunt. Morbi volutpat non quam sed sollicitudin. In id
                    dignissim sapien. Nullam ut leo a arcu pharetra rutrum.
                  </p>
                </Tabs.Pane>
              </Tabs.Container>
              <div>Other underline tabs may be configured:</div>

              <Tabs.Container id="side-panel-subtabs" variant="underline">
                <Tabs.Pane title="Cleaning">How to clean the car</Tabs.Pane>
                <Tabs.Pane title="Maintenance">
                  How to maintain the car
                </Tabs.Pane>
              </Tabs.Container>
              <footer className="border-t">
                The side panel can contain a footer
              </footer>
            </SidePanel>
          </div>
        )}
      </div>
    )
  },
}

.spinner-border {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  border: 3.2px solid currentColor;
  border-radius: 50%;
  border-right-color: transparent;
  vertical-align: text-bottom;
  animation: spinner-border 0.75s linear infinite;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 4px;
}

.spinner-grow {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: currentColor;
  vertical-align: text-bottom;
  animation: spinner-grow 0.75s linear infinite;
  opacity: 0;
}

.spinner-grow-sm {
  width: 1rem;
  height: 1rem;
}

@keyframes spinner-border {
  to {
    transform: rotate(360deg);
  }
}

@keyframes spinner-grow {
  0% {
    transform: scale(0);
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

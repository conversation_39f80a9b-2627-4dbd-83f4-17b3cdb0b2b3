import { withActions } from '@storybook/addon-actions/decorator'
import type { Meta, StoryObj } from '@storybook/react'
import { DropDown } from '.'

const meta: Meta<typeof DropDown.ToggleMenu> = {
  title: 'Components/DropDown',
  component: DropDown.ToggleMenu,
  parameters: {
    layout: 'padded',
    actions: { handles: ['click'] },
  },
  decorators: [withActions],
  subcomponents: { Item: DropDown.Item },
}

export default meta
type Story = StoryObj<typeof meta>

export const Primary: Story = {
  name: 'DropDown',
  args: {
    label: 'Toggle me',
    children: [
      <DropDown.Item href="/destination" label="Go somewhere" key="link" />,
      <DropDown.Item
        onClick={() => console.log('Button clicked!')}
        label="Do something"
        key="button"
      />,
      <DropDown.Item
        Component={({ onClick, children, ...props }) => (
          <span onClick={onClick} {...props}>
            Custom: {children}
          </span>
        )}
        onClick={() => console.log('Custom component clicked!')}
        label="Do something else"
        key="custom"
      />,
    ],
  },
}

import { SVGProps } from 'react'

export const NotFoundIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" {...props}>
    <g clipPath="url(#a)">
      <path
        className="dark:fill-[#707B82] fill-[#A6A7A8]"
        d="M7.709 68.462a1.679 1.679 0 0 1-1.676-1.683V19.28h72.843v18.102a12.204 12.204 0 0 1 3.028-.377c1.026 0 2.052.126 3.028.377V6.652C84.932 3.037 82.003.1 78.399.1H6.508C2.903.1 0 3.037 0 6.652v60.127a7.692 7.692 0 0 0 7.709 7.733H52.47l3.603-6.05H7.71ZM32.412 6.15c1.927 0 3.504 1.581 3.504 3.514a3.499 3.499 0 0 1-3.504 3.49 3.476 3.476 0 0 1-3.479-3.49c0-1.933 1.552-3.514 3.479-3.514Zm-10.587 0c1.927 0 3.504 1.581 3.504 3.514a3.499 3.499 0 0 1-3.505 3.49 3.476 3.476 0 0 1-3.478-3.49c0-1.933 1.552-3.514 3.479-3.514Zm-10.588 0c1.928 0 3.479 1.581 3.479 3.514a3.477 3.477 0 0 1-3.479 3.49 3.498 3.498 0 0 1-3.503-3.49c0-1.933 1.577-3.514 3.503-3.514Z"
      />
      <path
        className="dark:fill-[#707B82] fill-[#A6A7A8]"
        d="M18.17 59.725h43.11l3.28-5.523H18.17a2.748 2.748 0 0 0-2.752 2.762 2.749 2.749 0 0 0 2.752 2.761ZM18.176 46.633h14.89a2.757 2.757 0 0 0 2.752-2.762V30.787a2.757 2.757 0 0 0-2.752-2.762h-14.89a2.757 2.757 0 0 0-2.752 2.762v13.084a2.757 2.757 0 0 0 2.752 2.762ZM66.737 28.025H43.266a2.757 2.757 0 0 0-2.752 2.762 2.757 2.757 0 0 0 2.752 2.762h23.47a2.757 2.757 0 0 0 2.753-2.762 2.757 2.757 0 0 0-2.752-2.762ZM69.489 43.87a2.757 2.757 0 0 0-2.752-2.762H43.266a2.757 2.757 0 0 0-2.752 2.762 2.757 2.757 0 0 0 2.752 2.762h23.47a2.757 2.757 0 0 0 2.753-2.762Z"
      />
      <path
        className="dark:fill-[#9DA4A9] fill-[#818182]"
        d="m107.104 79.626-19.842-33.3a6.186 6.186 0 0 0-5.358-3.05 6.185 6.185 0 0 0-5.358 3.05l-19.84 33.3a6.205 6.205 0 0 0-.073 6.301c1.124 1.987 3.153 3.173 5.43 3.173h39.683c2.276 0 4.306-1.186 5.43-3.173a6.207 6.207 0 0 0-.072-6.3ZM78.926 57.44a2.992 2.992 0 0 1 2.98-2.988 2.99 2.99 0 0 1 2.977 2.988l.002 12.41a2.993 2.993 0 0 1-2.98 2.989 2.994 2.994 0 0 1-2.978-2.988V57.439h-.001Zm2.98 25.753a3.639 3.639 0 0 1-3.635-3.645 3.64 3.64 0 0 1 3.634-3.645 3.64 3.64 0 0 1 3.634 3.645 3.64 3.64 0 0 1-3.634 3.645Z"
      />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M0 .1h108v89H0z" />
      </clipPath>
    </defs>
  </svg>
)

import { InputNumber } from '@mescius/wijmo.react.input'
import { ComponentProps, useRef, useState } from 'react'
import { ReadOnlyInput } from '../ReadOnlyInput'
const FORMATS = {
  number: 'n',
  percent: 'p',
  currency: 'c',
} as const

export type Format = keyof typeof FORMATS

const FORMAT_FUNCTIONS = {
  percent: (v: number) => `${v * 100}%`,
  currency: (v: number) => `$${v}`,
  number: (v: number) => v,
}

function fmt(value: number, format: Format = 'number') {
  return FORMAT_FUNCTIONS[format](value)
}

type Props = {
  /** The increment used by the +/- buttons */
  step: number
  /** The id of the component. This will come from the form field component. */
  id?: string
  /** The format of the number:
   * 'number', 'percent', or 'currency'
   * */
  format?: Format
  /** Whether or not the component is disabled **/
  disabled?: boolean
  /** The initial value of the input */
  initialValue?: number
  /** Aria props for connecting error and description messages. This will come from the form field and need to be passed to the wijmo component */
  'aria-describedby'?: string
  /** Aria props for communicating errors. this will come from the form field and need to be passed to the wijmo component */
  'aria-invalid'?: boolean
  /** Aria props for label. this will come from the form field and need to be passed to the wijmo component */
  'aria-label'?: string
  /** The function that will be called when the value changes */
  onChange?: (value: number) => void
  /** The min value allowed */
  min?: number
  /** The max value allowed */
  max?: number
  /** Whether the input is readOnly */
  readOnly?: boolean
}
const getPrecision = (step: number, format: Format) => {
  let precision = step.toString().split('.')[1]?.length ?? 0
  if (format === 'percent' && precision >= 2) {
    precision = precision - 2
  } else if (format === 'currency') {
    precision = 2
  }
  return precision
}
/**
 * The NumberInput component is used to allow users to input a number.
 * The component is built on top of the Wijmo InputNumber component.
 * It should be used alongside the FormField component to provide a label and error messages.
 * If you use it without the FormField component, provide an ID and label or aria-label for accessibility.
 */
const NumberInput = ({
  step = 1,
  disabled = false,
  format = 'number',
  id,
  initialValue = undefined,
  'aria-describedby': ariaDescribedBy,
  'aria-invalid': ariaInvalid,
  'aria-label': ariaLabel,
  onChange,
  readOnly,
  ...props
}: Props) => {
  const [value, setValue] = useState(initialValue)

  const onChangeRef = useRef(onChange)
  onChangeRef.current = onChange

  const handleChange: ComponentProps<typeof InputNumber>['valueChanged'] = (
    event,
  ) => {
    // This is a workaround for the Wijmo component
    // which runs onChange during initialization
    if (event.inputElement.dataset.loaded !== 'true') return
    if (event.text.length > 0) {
      event.inputElement.size = event.text.length
    }
    if (onChangeRef.current) onChangeRef.current(event.value!)
    setValue(event.value ?? undefined)
  }

  const inputMode = Math.floor(step) === step ? 'numeric' : 'decimal'
  const precision = getPrecision(step, format)
  return readOnly ? (
    <ReadOnlyInput
      id={id}
      value={value !== undefined ? fmt(value, format) : '--'}
      aria-label={ariaLabel}
    />
  ) : (
    <InputNumber
      id={id}
      format={FORMATS[format] + precision}
      step={step}
      isDisabled={disabled}
      className="number-input"
      aria-describedby={ariaDescribedBy}
      aria-invalid={ariaInvalid}
      aria-label={ariaLabel}
      initialized={({ inputElement }: { inputElement: HTMLInputElement }) => {
        inputElement.inputMode = inputMode
        inputElement.size = initialValue?.toString().length ?? 1
        inputElement.dataset.loaded = 'true'
      }}
      valueChanged={handleChange}
      inputType="text"
      isRequired={false}
      placeholder="--"
      {...props}
      value={value}
    />
  )
}

export { fmt as formatNumber, NumberInput }

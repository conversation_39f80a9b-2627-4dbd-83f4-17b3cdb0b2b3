import { render, screen } from '@/tests/react'
import { axe } from 'jest-axe'
import { SearchBarItem } from './SearchBarItem'

describe('SearchBaritem', () => {
  test('renders an icon with correct children', () => {
    render(
      <SearchBarItem ariaLabel="Search Bar Item">
        <span>Search Bar Item</span>
      </SearchBarItem>,
    )

    const searchBarItem = screen.getByLabelText('Search Bar Item')

    expect(searchBarItem).toBeInTheDocument()
  })

  test('adheres to accessibility guidelines', async () => {
    const { container } = render(
      <SearchBarItem ariaLabel="Search Bar Item">
        <span>Search Bar Item</span>
      </SearchBarItem>,
    )
    expect(await axe(container)).toHaveNoViolations()
  })
})

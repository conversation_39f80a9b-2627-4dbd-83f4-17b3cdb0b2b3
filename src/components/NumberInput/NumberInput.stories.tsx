import { WijmoLicenseProvider } from '@/wijmo'
import { withActions } from '@storybook/addon-actions/decorator'
import type { Meta, StoryObj } from '@storybook/react'
import { NumberInput } from '.'

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta: Meta<typeof NumberInput> = {
  title: 'Components/NumberInput',
  component: NumberInput,
  parameters: {
    layout: 'centered',
    actions: { handles: ['click'] },
  },
  decorators: [
    (Story) => (
      <WijmoLicenseProvider license="">
        <div className="p-xl rounded docs-foreground">{Story()}</div>
      </WijmoLicenseProvider>
    ),
    withActions,
  ],
}

export default meta
type Story = StoryObj<typeof meta>

export const Primary: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Number inputs are used any place we want to allow users to input a specific number.',
      },
    },
  },
  args: {
    step: 0.01,
    format: 'percent',
    min: 0,
    max: 1,
  },
}

export const Disabled: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Disabled number inputs are used any place we want to remove an input from the site's interactive flow based on some business logic",
      },
    },
  },
  args: {
    step: 1.3,
    disabled: true,
    format: 'percent',
  },
}

import { useApplication } from './useApplication'

/**
 * Get a function that checks for the presence of a specific permission in the
 * user's authorizations for the current application, applicable to the
 * specified scope.
 *
 * This performs some filtering to ignore authorizations for irrelevant scopes,
 * or for other applications, and allows the consumer to then run a simplified
 * function to determine the presence of a basic _relevant_ permission for the
 * user.
 */
export function useHasPermission(scope: string) {
  const { user, application } = useApplication()
  const authorizations = user.authorizations.authorizations[application]
  const scopedAuthorizations = authorizations.filter(
    (authorization) =>
      authorization.scope === scope || authorization.scope === null,
  )

  return (permission: string) =>
    scopedAuthorizations.some((authorization) =>
      authorization.permissions.includes(permission),
    )
}

.choice-group {
  display: flex;
  flex-wrap: wrap;
  align-items: start;

  justify-content: start;

  gap: var(--gatx-spacing-sm);

  &[data-layout='vertical'] {
    flex-direction: column;
    flex-wrap: nowrap;
    margin-left: calc(var(--gatx-spacing-xs) * -1);
    padding: 0 var(--gatx-spacing-xs);
  }

  &[data-required='true'] {
    border-radius: var(--gatx-border-radius);
    outline: 1px solid var(--gatx-colors-warning-4);
  }
}

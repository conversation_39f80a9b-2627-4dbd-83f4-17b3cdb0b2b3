import { render, screen } from '@/tests/react'
import { axe } from 'jest-axe'
import { Heading, Landmark } from '.'

describe('Heading', () => {
  test.each([1, 2, 3, 4, 5, 6])(
    'uses default HTML tag for heading level %d',
    async (level) => {
      const { container } = render(<Heading level={level}>Header Text</Heading>)

      const heading = screen.getByRole('heading', { name: 'Header Text' })
      expect(heading.tagName).toEqual(`H${level}`)

      expect(await axe(container)).toHaveNoViolations()
    },
  )

  test.each([7, 8, 9])(
    'renders an accessible div for heading level %d',
    async (level) => {
      const { container } = render(<Heading level={level}>Header Text</Heading>)

      const heading = screen.getByRole('heading', { name: 'Header Text' })
      expect(heading.tagName).toEqual('DIV')
      expect(heading).toHaveAttribute('aria-level', `${level}`)

      expect(await axe(container)).toHaveNoViolations()
    },
  )

  test('inherits the heading level from a parent Landmark', () => {
    render(
      <Landmark level={3}>
        <Heading>Some Header Text</Heading>
      </Landmark>,
    )

    const heading = screen.getByRole('heading', { name: 'Some Header Text' })
    expect(heading.tagName).toEqual('H3')
  })
})

import { render, screen } from '@/tests/react'
import userEvent, { PointerEventsCheckLevel } from '@testing-library/user-event'
import { axe } from 'jest-axe'
import { CloseButton } from '.'

describe('CloseButton', () => {
  test('triggers onClick when clicked', async () => {
    const onClick = jest.fn()

    render(<CloseButton onClick={onClick} />)

    await userEvent.click(screen.getByRole('button', { name: 'Close' }))

    expect(onClick).toHaveBeenCalled()
  })

  test('does not trigger onClick when clicked and disabled', async () => {
    const onClick = jest.fn()

    render(<CloseButton onClick={onClick} disabled />)

    await userEvent.click(screen.getByRole('button', { name: 'Close' }), {
      pointerEventsCheck: PointerEventsCheckLevel.Never,
    })

    expect(onClick).not.toHaveBeenCalled()
  })

  test('adheres to accessibility guidelines', async () => {
    const { container } = render(<CloseButton onClick={jest.fn()} />)
    expect(await axe(container)).toHaveNoViolations()
  })
})

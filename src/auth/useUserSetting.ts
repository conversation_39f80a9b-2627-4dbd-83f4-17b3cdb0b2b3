import { useLocalStorage } from '../stores/LocalStorage/useLocalStorage'
import { useUser } from './useUser'

/**
 * Get a named setting for the logged-in User. Leverages the `useLocalStorage`
 * hook with a prefixed key that isolates the value to the current User, meaning
 * that multiple users can access the same application and make different
 * settings choices without causing any conflicts.
 */
export function useUserSetting<T>(key: string, defaultValue: T) {
  const user = useUser()
  return useLocalStorage(`${user.id}:${key}`, defaultValue)
}

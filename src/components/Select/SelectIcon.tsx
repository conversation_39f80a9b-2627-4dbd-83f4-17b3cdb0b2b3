import clsx from 'clsx'
import * as Icons from 'react-bootstrap-icons'

type IconName = keyof typeof Icons

type Props = {
  icon: string
  isDisabled?: boolean
}

const SelectIcon = ({ icon, isDisabled }: Props) => {
  const IconComponent = icon ? Icons[icon as IconName] : null

  return (
    IconComponent && (
      <IconComponent
        aria-hidden
        className={clsx(
          'search-bar-icon',
          isDisabled ? 'search-bar-icon-disabled' : '',
        )}
      />
    )
  )
}

export { SelectIcon }

.radio-choice {
  --gatx-radio-bg: var(--gatx-colors-light-2);
  --gatx-radio-border-color: var(--gatx-colors-dark-5);
  --gatx-radio-selected-color: var(--gatx-colors-primary-7);
  --gatx-radio-disabled-color: var(--gatx-colors-light-8);

  @media (prefers-color-scheme: dark) {
    --gatx-radio-bg: var(--gatx-colors-dark-6);
    --gatx-radio-border-color: var(--gatx-colors-dark-2);
    --gatx-radio-selected-color: var(--gatx-colors-secondary-5);
    --gatx-radio-disabled-color: var(--gatx-colors-dark-4);
  }

  display: flex;
  align-items: center;
  justify-content: center;

  width: 16px;
  height: 16px;
  margin-top: var(--gatx-spacing-xs);
  border: 1px solid var(--gatx-radio-border-color);
  border-radius: 50%;
  background: transparent;
  cursor: pointer;

  &:not(:has(input:disabled)) {
    &:active,
    &:has(input:active),
    &:has(input:checked) {
      border: none;
      color: var(--gatx-radio-selected-color);

      &:focus-within {
        border: none;
      }
    }
  }

  &:has(input:disabled) {
    border: 1px solid var(--gatx-radio-disabled-color);
    background: var(--gatx-radio-bg);

    &:active,
    &:has(input:active),
    &:has(input:checked) {
      border: none;
      color: var(--gatx-radio-disabled-color);
    }
  }

  &:focus-within {
    outline: 4px solid
      color-mix(in srgb, var(--gatx-radio-selected-color), transparent 40%);
  }
}

.radio-label {
  --gatx-radio-selected-label-color: var(--gatx-colors-dark-8);

  @media (prefers-color-scheme: dark) {
    --gatx-radio-selected-label-color: var(--gatx-colors-light-1);
  }

  cursor: pointer;

  &:not(:has(input:disabled)) {
    &:active,
    &:has(input:active),
    &:has(input:checked) {
      color: var(--gatx-radio-selected-label-color);
    }
  }
}

'use client'

import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'

import { enResources } from './resources/en'

const resources = {
  en: enResources,
} as const
export type i18nResources = typeof resources
const namespaceKeys = Object.keys(resources.en)

if (!i18n.isInitialized) {
  i18n
    .use(initReactI18next)
    .init({
      resources,
      lng: 'en',
      fallbackLng: 'en',
      ns: namespaceKeys,
      interpolation: {
        escapeValue: false,
      },
    })
    .catch(() => console.error('Error initializing i18n'))
}

export { i18n }

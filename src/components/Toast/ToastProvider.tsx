'use client'
import { createContext, PropsWithChildren, use, useRef, useState } from 'react'
import { IconName, Toast } from '.'

type ToastContext = {
  showToast: (message: Message) => void
}

type Message = {
  text?: string
  level: 'info' | 'error' | 'success'
  title: string
}

const ICONS: Record<Message['level'], IconName | 'Spinner'> = {
  error: 'ExclamationTriangleFill',
  info: 'Spinner',
  success: 'CheckCircleFill',
}

const ToastContext = createContext<ToastContext>({
  showToast: () => {
    throw new Error('ToastProvider not initialized')
  },
})

const ToastProvider = ({ children }: PropsWithChildren) => {
  const [message, setMessage] = useState<Message | undefined>()
  const icon = message ? ICONS[message.level] : undefined

  const msg = useRef<Message | undefined>(undefined)
  msg.current = message ?? msg.current

  return (
    <ToastContext.Provider value={{ showToast: setMessage }}>
      <Toast
        variant={msg.current?.level ?? 'info'}
        show={!!message}
        icon={icon}
        title={msg.current?.title ?? ''}
        text={msg.current?.text ?? ''}
        onClose={() => {
          setMessage(undefined)
        }}
      />

      {children}
    </ToastContext.Provider>
  )
}

function useShowToast() {
  return use(ToastContext).showToast
}

export { ToastProvider, useShowToast }

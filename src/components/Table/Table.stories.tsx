import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'
import { Table } from '.'
import { Button } from '../Button'

const meta: Meta<typeof Table> = {
  title: 'Components/Table',
  component: Table,
  parameters: {
    layout: 'padded',
  },
  decorators: [
    (Story) => (
      <div className="p-sm rounded docs-foreground">
        <Story />
      </div>
    ),
  ],
}

export default meta
type Story = StoryObj<typeof meta>

export const ColumnHeaders: Story = {
  parameters: {
    docs: {
      description: {
        story: `Column headers should be used in situations where each row of
          data is structured with a consistent set of details. The headers
          provide the necessary context for what each column contains.`,
      },
    },
  },
  args: {
    columnHeaders: ['Program', 'Program Description', 'Due'],
    rows: [
      ['EN-667', 'INSPECT JBURY AZFRC INSERT', '22 APR 2014'],
      ['EN-690', 'DO NOT OVERPAINT RAILCARS', '30 OCT 2015'],
      ['IO-533-1', 'PETRO CANADA LUBRICANTS', '22 JAN 2017'],
    ],
  },
}

export const RowHeaders: Story = {
  parameters: {
    docs: {
      description: {
        story: `Row headers should be used in situations where the individual
          rows of data represent unique pieces of a larger context. This pattern
          will be used most commonly for rendering information structured as a
          list of key-value pairs. The first element of each row will be treated
          as the header for that row.

          If there are more than two columns in a rowHeaders table,
          columnHeaders need to be provided for a11y but will be screen reader.`,
      },
    },
  },
  args: {
    hasRowHeaders: true,
    columnHeaders: ['Name', 'Value', 'Document'],
    rows: [
      ['Stenciled Class/Car Style', 'DOT 111A100-W-1', null],
      ['Product Code', 'GSL0016', null],
      [
        'Certificate of Construction',
        'L986099A',
        <Button key={1} size="small" text="Download" />,
      ],
      ['Mechanical Designation', 'T - Tank', null],
    ],
  },
}

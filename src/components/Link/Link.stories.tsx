import { <PERSON>a, StoryObj } from '@storybook/react'
import { <PERSON> } from '.'

const meta: Meta<typeof Link> = {
  title: 'Components/Link',
  component: Link,
}

export default meta
type Story = StoryObj<typeof meta>

export const Underline: Story = {
  name: 'Variant: Underline',
  parameters: {
    docs: {
      description: {
        story: 'Generic reusable link component for dark and light mode',
      },
    },
  },
  args: {
    variant: 'underline',
    children: 'Link Title',
    'aria-label': 'Link Title',
    href: 'https://www.gatx.com/',
  },
}

export const Outline: Story = {
  name: 'Variant: Outline',
  parameters: {
    docs: {
      description: {
        story: 'Variant that allows links to be styled as a button.',
      },
    },
  },
  args: {
    variant: 'outline',
    children: 'Link Title',
    'aria-label': 'Link Title',
    href: 'https://www.gatx.com/',
  },
}

import { render } from '@/tests/react'
import { axe } from 'jest-axe'
import { FilterPillGroup } from '.'

describe('FilterPillGroup', () => {
  const filters = [
    { value: 'material', text: 'Material Type', filterSource: 'materialType' },
  ]
  const onRemove = (value: string, filterSource: string) =>
    console.log(value, filterSource)

  test('adheres to accessibility guidelines', async () => {
    const { container } = render(
      <FilterPillGroup appliedFilters={filters} removeFilter={onRemove} />,
    )

    expect(await axe(container)).toHaveNoViolations()
  })
})

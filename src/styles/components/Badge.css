/**
* Base Component Definition
*/
.badge {
  --gatx-badge-padding: 0.3rem 0.5rem;
  --gatx-badge-border: 1px solid;
  --gatx-badge-border-radius: 50rem;

  --gatx-badge-color-mono: unset;

  --gatx-badge-color: unset;
  --gatx-badge-color-dark: unset;

  --gatx-badge-bg: unset;
  --gatx-badge-bg-dark: unset;

  --gatx-badge-border-color: unset;
  --gatx-badge-border-color-dark: unset;

  @media (prefers-color-scheme: dark) {
    --gatx-badge-bg: var(--gatx-badge-bg-dark) !important;
    --gatx-badge-color: var(--gatx-badge-color-dark) !important;
    --gatx-badge-border-color: var(--gatx-badge-border-color-dark) !important;
  }

  height: fit-content;

  padding: var(--gatx-badge-padding);
  border: var(--gatx-badge-border) var(--gatx-badge-border-color);
  border-radius: var(--gatx-badge-border-radius);

  background-color: var(--gatx-badge-bg);
  color: var(--gatx-badge-color);
  font-size: var(--gatx-typography-font-size-xs);
}

/**
* Variants - Style
*/
.badge-primary {
  --gatx-badge-bg: var(--gatx-colors-secondary-8);
  --gatx-badge-bg-dark: var(--gatx-colors-secondary-4);

  --gatx-badge-color: var(--gatx-colors-light-1);
  --gatx-badge-color-dark: var(--gatx-colors-dark-8);
  --gatx-badge-color-mono: var(--gatx-colors-secondary-4);

  --gatx-badge-border-color: var(--gatx-colors-secondary-8);
}

.badge-outline {
  --gatx-badge-color: var(--gatx-colors-secondary-9);
  --gatx-badge-color-dark: var(--gatx-colors-secondary-2);

  --gatx-badge-border-color: var(--gatx-colors-secondary-9);
  --gatx-badge-border-color-dark: var(--gatx-colors-secondary-2);

  --gatx-badge-color-mono: var(--gatx-colors-secondary-2);
}

.badge-info {
  --gatx-badge-bg: var(--gatx-colors-primary-2);
  --gatx-badge-bg-dark: var(--gatx-colors-primary-2);

  --gatx-badge-color: var(--gatx-colors-primary-9);
  --gatx-badge-color-dark: var(--gatx-colors-primary-9);

  --gatx-badge-border-color: var(--gatx-colors-primary-6);
  --gatx-badge-border-color-dark: var(--gatx-colors-primary-5);

  --gatx-badge-color-mono: var(--gatx-colors-primary-2);
}

.badge-warning {
  --gatx-badge-bg: var(--gatx-colors-warning-1);
  --gatx-badge-bg-dark: var(--gatx-colors-warning-1);

  --gatx-badge-color: var(--gatx-colors-warning-8);
  --gatx-badge-color-dark: var(--gatx-colors-warning-8);

  --gatx-badge-border-color: var(--gatx-colors-warning-4);
  --gatx-badge-border-color-dark: var(--gatx-colors-warning-5);

  --gatx-badge-color-mono: var(--gatx-colors-warning-2);
}

.badge-error {
  --gatx-badge-bg: var(--gatx-colors-error-1);
  --gatx-badge-bg-dark: var(--gatx-colors-error-1);

  --gatx-badge-color: var(--gatx-colors-error-8);
  --gatx-badge-color-dark: var(--gatx-colors-error-8);

  --gatx-badge-border-color: var(--gatx-colors-error-4);
  --gatx-badge-border-color-dark: var(--gatx-colors-error-5);

  --gatx-badge-color-mono: var(--gatx-colors-error-2);
}

.badge-success {
  --gatx-badge-bg: var(--gatx-colors-success-1);
  --gatx-badge-bg-dark: var(--gatx-colors-success-1);

  --gatx-badge-color: var(--gatx-colors-success-8);
  --gatx-badge-color-dark: var(--gatx-colors-success-8);

  --gatx-badge-border-color: var(--gatx-colors-success-4);
  --gatx-badge-border-color-dark: var(--gatx-colors-success-5);

  --gatx-badge-color-mono: var(--gatx-colors-success-2);
}

.badge-disabled {
  --gatx-badge-bg: var(--gatx-colors-light-4);
  --gatx-badge-bg-dark: var(--gatx-colors-dark-2);

  --gatx-badge-color: var(--gatx-colors-light-10);
  --gatx-badge-color-dark: var(--gatx-colors-dark-6);

  --gatx-badge-border-color: var(--gatx-colors-light-8);
  --gatx-badge-border-color-dark: var(--gatx-colors-dark-5);

  --gatx-badge-color-mono: var(--gatx-colors-dark-2);
}

/**
* Variants - Sizes
*/
.badge-sm {
  display: flex;
  align-items: center;
  justify-content: center;
  aspect-ratio: 1 / 1;
  height: 1rem;
  padding: 0 4px;

  --gatx-badge-border-color: var(--gatx-badge-color-mono);
  --gatx-badge-border-color-dark: var(--gatx-badge-color-mono);
  --gatx-badge-color: var(--gatx-badge-color-dark);

  &:not(.badge-outline) {
    --gatx-badge-bg: var(--gatx-badge-color-mono);
    --gatx-badge-bg-dark: var(--gatx-badge-color-mono);
  }
}

.badge-md {
  text-align: center;
  --gatx-badge-padding: 0 4px;
  --gatx-badge-border-radius: 4px;
  --gatx-badge-border-color: var(--gatx-badge-color-mono);
  --gatx-badge-border-color-dark: var(--gatx-badge-color-mono);

  --gatx-badge-color: var(--gatx-badge-color-dark);

  &:not(.badge-outline) {
    --gatx-badge-bg: var(--gatx-badge-color-mono);
    --gatx-badge-bg-dark: var(--gatx-badge-color-mono);
  }
}

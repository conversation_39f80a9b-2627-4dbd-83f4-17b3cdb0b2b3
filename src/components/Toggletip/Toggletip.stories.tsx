import { withActions } from '@storybook/addon-actions/decorator'
import type { Meta, StoryObj } from '@storybook/react'
import { Toggletip } from './index'

const meta: Meta<typeof Toggletip> = {
  title: 'Components/Toggletip',
  component: Toggletip,
  parameters: {
    layout: 'padded',
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: { disabled: false },
  parameters: {
    layout: 'centered',
    actions: { handles: ['click'] },
  },
  decorators: [withActions],
}

.toast-container {
  z-index: 2;
  position: absolute;
  margin: 10px 13px;

  &.top-0 {
    top: 0;
  }

  &.end-0 {
    right: 0;
  }
}

.toast {
  --gatx-toast-bg: unset;
  --gatx-toast-color: unset;
  --gatx-toast-border: unset;
  --gatx-toast-icon-color: unset;

  display: flex;
  align-items: center;
  width: 350px;
  padding: 12px 20px;
  border: 1px solid var(--gatx-toast-border);
  border-radius: var(--gatx-border-radius);
  background-color: var(--gatx-toast-bg);
  box-shadow: 0px 8px 16px 0px #00000026;
  color: var(--gatx-toast-color);

  .toast-header {
    font-weight: var(--gatx-typography-font-weight-bold);
  }

  .toast-body {
    font-weight: var(--gatx-typography-font-weight-regular);

    &::before {
      margin: 0 4px;
      content: '-';
    }
  }

  .icon {
    margin-left: auto;
    color: var(--gatx-toast-icon-color);
  }
}

.toast-info {
  --gatx-toast-bg: var(--gatx-colors-primary-2);
  --gatx-toast-border: var(--gatx-colors-primary-6);
  --gatx-toast-color: var(--gatx-colors-primary-9);
  --gatx-toast-icon-color: var(--gatx-colors-primary-6);

  @media (prefers-color-scheme: dark) {
    --gatx-toast-icon-color: var(--gatx-colors-primary-5);
  }
}

.toast-success {
  --gatx-toast-bg: var(--gatx-colors-success-2);
  --gatx-toast-border: var(--gatx-colors-success-4);
  --gatx-toast-color: var(--gatx-colors-success-8);
  --gatx-toast-icon-color: var(--gatx-colors-success-5);
}

.toast-error {
  --gatx-toast-bg: var(--gatx-colors-error-2);
  --gatx-toast-border: var(--gatx-colors-error-5);
  --gatx-toast-color: var(--gatx-colors-error-8);
  --gatx-toast-icon-color: var(--gatx-colors-error-5);

  @media (prefers-color-scheme: dark) {
    --gatx-toast-border: var(--gatx-colors-error-4);
    --gatx-toast-icon-color: var(--gatx-colors-error-4);
  }
}

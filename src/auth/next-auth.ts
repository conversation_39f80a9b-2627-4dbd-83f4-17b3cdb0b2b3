import NextAuth, { type NextAuthResult } from 'next-auth'
import { NextResponse } from 'next/server'
import { Authorization } from './types/Authorization'
import { Resource } from './types/Resource'

const secure = process.env.AUTH_PROVIDER_URL?.startsWith('https://')
const config = NextAuth({
  /**
   * NOTE: PlatformOne services do not perform their own authentication --
   * rather, they redirect the user to the landing page with an appropriate
   * callback URL. We leave the `providers` array here empty to make it clear
   * that this service does not have its own authentication mechanism.
   */
  providers: [],
  secret: process.env.AUTH_SECRET,
  cookies: {
    sessionToken: {
      name: 'platform-one.session-token',
      options: {
        domain: process.env.AUTH_DOMAIN,
        path: '/',
        httpOnly: true,
        sameSite: 'lax',
        secure,
      },
    },
  },
  callbacks: {
    /**
     * Determine whether or not authentication has been successful.
     */
    signIn({ user }) {
      if (user) return true
      return false
    },
    /**
     * Fetch the user's authorization metadata, and attach it to the generated
     * JWT.
     */
    jwt({ token }) {
      return token
    },
    /**
     * Fetch the authorization metadata from the JWT, and add it to the session.
     */
    session({ session, token }) {
      session.user.id = token.id as string
      session.user.authorizations = token.authorizations as Authorization
      session.user.resources = token.resources as Resource[]
      session.user.external = !!token.external

      return session
    },
    /**
     * Determine whether the user is permitted to access the site, or if they
     * should be redirected to the login page.
     *
     * NOTE: Our middleware stack is configured to ignore static assets, so we
     * don't need to exclude those URLs here.
     */
    authorized({ request, auth }) {
      if (auth) {
        return true
      }

      const callbackUrl = new URL(
        request.nextUrl.pathname,
        process.env.AUTH_URL,
      ).toString()

      // Logged in users are authenticated, otherwise redirect to login page
      const params = new URLSearchParams({ callbackUrl })
      const loginURL = new URL(
        `/api/auth/signin?${params.toString()}`,
        process.env.AUTH_PROVIDER_URL,
      )

      return NextResponse.redirect(loginURL)
    },
  },
  debug: process.env.NODE_ENV === 'development',
})

const { handlers, signIn, signOut } = config

const auth: NextAuthResult['auth'] = config.auth

export { auth, handlers, signIn, signOut }

import { createEvent<PERSON><PERSON> } from '@/utils/createEventKey'
import clsx from 'clsx'
import { Children, ReactElement } from 'react'
import {
  Nav,
  NavItem,
  NavLink,
  TabContainer,
  TabContent,
  TabsProps,
} from 'react-bootstrap'
import { Pane, Props as PaneProps } from './Pane'

type Props = TabsProps & {
  /** The variant of the tab component. See the stories below for supported variants. */
  variant?: 'underline' | 'pills'
  /** The ID of the container element for the tab group. */
  id: string
  /** The tabs that should be rendered within the container. */
  children: ReactElement<PaneProps> | ReactElement<PaneProps>[]
  /** The className of the tab content wrapper */
  contentClassName?: string
}

/**
 * The Tab component is used to condense the display of different pieces of
 * related information into groups, in an effort to make effective use of
 * limited screen real estate and limit the need for vertical scrolling.
 *
 * The component consists of two elements that can be composed together:
 * - `Tab.Container` is a parent element that provides context for the content
 *   contained within.
 * - `Tab.Pane` is a child element that contains the information for one of the
 *   groups of information represented by the tab block.
 *
 * This element renders all content on the page, just using display styles to
 * hide the unopened tabs, which makes it suitable for any printing or
 * accessibility needs that require access to the full rendered child DOM.
 */
const Container = ({
  id,
  variant = 'underline',
  children,
  contentClassName,
  ...props
}: Props) => {
  const tabs = Children.map(children, (element) => {
    const { title } = element.props
    return { title, key: createEventKey(title) }
  })

  return (
    <TabContainer {...props} id={id} defaultActiveKey={tabs[0].key}>
      <Nav
        variant="tabs"
        className={clsx(`tabs-${variant}`, props.className)}
        as="ul"
      >
        {tabs.map(({ key, title }) => {
          return (
            <NavItem as="li" key={key} role="presentation">
              <NavLink eventKey={key} as="button">
                {title}
              </NavLink>
            </NavItem>
          )
        })}
      </Nav>
      <TabContent className={contentClassName}>{children}</TabContent>
    </TabContainer>
  )
}

const Tabs = { Container, Pane }
export { Pane } from './Pane'
export { Tabs }

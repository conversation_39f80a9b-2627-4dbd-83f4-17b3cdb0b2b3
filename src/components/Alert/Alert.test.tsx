import { render, RenderResult } from '@testing-library/react'
import { axe } from 'jest-axe'
import { Alert } from '.'

describe('Alert', () => {
  let component: RenderResult

  describe.each([
    { urgency: 'immediate', role: 'alert' },
    { urgency: 'deferred', role: 'status' },
    { urgency: 'inline', role: undefined },
  ] as const)(
    'when the urgency is "$urgency" and the variant is "$variant"',
    ({ urgency, role }) => {
      describe.each(['error', 'warning'] as const)(
        'and the level is "%s"',
        (level) => {
          beforeEach(() => {
            component = render(
              <Alert title="This is an alert!" level={level} urgency={urgency}>
                This is a detailed description of what happened
              </Alert>,
            )
          })

          if (role) {
            it(`has role "${role}"`, () => {
              const alert = component.getByRole(role)

              expect(alert).toBeInTheDocument()
            })
          } else {
            it('does not have alert or status roles', () => {
              expect(component.queryByRole('alert')).not.toBeInTheDocument()
              expect(component.queryByRole('status')).not.toBeInTheDocument()
            })
          }

          it('adheres to accessibility guidelines', async () => {
            const { container } = component

            expect(await axe(container)).toHaveNoViolations()
          })
        },
      )
    },
  )
})

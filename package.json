{"name": "@gatx-corp/platform-one-common", "publishConfig": {"registry": "https://npm.pkg.github.com/"}, "version": "0.0.0", "type": "module", "exports": {"./components/*": {"types": "./dist/types/components/*/index.d.ts", "import": "./dist/components/*.js", "require": "./dist/components/*.cjs"}, "./auth": {"types": "./dist/types/auth/index.d.ts", "import": "./dist/auth.js", "require": "./dist/auth.cjs"}, "./auth-server": {"types": "./dist/types/auth/next-auth.d.ts", "import": "./dist/auth-server.js", "require": "./dist/auth-server.cjs"}, "./stores/*": {"types": "./dist/types/stores/*/index.d.ts", "import": "./dist/stores/*.js", "require": "./dist/stores/*.cjs"}, "./wijmo": {"types": "./dist/types/wijmo.d.ts", "import": "./dist/wijmo.js", "require": "./dist/wijmo.cjs"}, "./icons/*": {"types": "./dist/types/icons/*.d.ts", "import": "./dist/icons/*.js", "require": "./dist/icons/*.cjs"}, ".": {"types": "./dist/types/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./styles": {"style": "./dist/styles.css", "import": "./dist/styles.css", "require": "./dist/styles.css"}, "./tailwind": {"style": "./dist/tailwind.css"}, "./postcss": {"types": "./dist/types/postcss.d.ts", "import": "./dist/postcss.js", "require": "./dist/postcss.cjs"}}, "scripts": {"dev": "STORYBOOK=true bun storybook", "storybook": "STORYBOOK=true storybook dev -p 6006", "storybook:build": "STORYBOOK=true storybook build", "lint": "eslint .", "format": "prettier --write .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "build": "rm -rf dist && vite build"}, "devDependencies": {"@rollup/plugin-typescript": "^12.1.2", "@storybook/addon-a11y": "^8.6.12", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/react-vite": "^8.6.12", "@storybook/test": "^8.6.12", "@tailwindcss/vite": "^4.1.5", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.13", "@types/jest-axe": "^3.5.9", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@typescript-eslint/eslint-plugin": "^8.2.0", "@typescript-eslint/parser": "^8.2.0", "@vitejs/plugin-react": "^4.4.1", "@vitest/browser": "^3.1.2", "@vitest/coverage-v8": "^3.1.2", "eslint": "^8", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-storybook": "^0.12.0", "glob": "^11.0.2", "jest": "^29.7.0", "jest-axe": "^9.0.0", "jest-environment-jsdom": "^29.7.0", "playwright": "^1.52.0", "prettier": "^3.3.3", "prettier-plugin-css-order": "^2.1.2", "prettier-plugin-organize-imports": "^4.0.0", "rollup-preserve-directives": "^1.1.3", "semantic-release": "^24.2.3", "storybook": "^8.6.12", "storybook-addon-render-modes": "^0.0.11", "storybook-dark-mode": "^4.0.2", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "tslib": "^2.8.1", "tsx": "^4.19.3", "typescript": "^5.8.3", "vite": "^6.2.0"}, "peerDependencies": {"@mescius/wijmo.react.input": "^5.20251.34", "@tailwindcss/postcss": "^4.1.5", "next-auth": "^5.0.0-beta.25", "postcss": "^8.4.41", "react": "^19.1.0", "react-bootstrap": "^2.10.4", "react-bootstrap-icons": "^1.11.5", "react-dom": "^19.1.0", "tailwindcss": "^4.1.5"}, "dependencies": {"clsx": "^2.1.1", "i18next": "^25.1.2", "postcss-dark-theme-class": "^1.3.0", "postcss-nesting": "^13.0.1", "react-i18next": "^15.5.1"}}
import { ComboBox as Wij<PERSON>ComboBox } from '@mescius/wijmo.input'
import {
  ComboBox,
  ItemTemplateContext,
  ItemTemplateRender,
} from '@mescius/wijmo.react.input'

import clsx from 'clsx'
import { HTMLProps, useCallback, useState } from 'react'
import { SelectIcon } from './SelectIcon'
import { SelectItem } from './SelectItem'

type Props = HTMLProps<HTMLElement> & {
  /** Identifier for the component. When using with the FormField component, this will come from that component. If you're using ths component without the FormField an ID is required for accessibility */
  id?: string
  /** Label to display and screenreader */
  label?: string
  /** Text to be displayed when there is no selection or initial value */
  placeholder?: string
  /** Static list of items  */
  items: any[]
  /** React element that will be used to display each item of the available options */
  itemTemplate?: ItemTemplateRender
  /** Event handler when selection changes */
  onSelectionChange?: (selectedItem: any) => void
  /** Size variant */
  variant?: 'md' | 'lg'
  /** Apply required styling to the component. Defaults to false */
  required?: boolean
  /** Component's disabled state */
  disabled?: boolean
}

export type SelectItem = { label: string; value: string }

type SelectTemplateContext = Omit<ItemTemplateContext, 'item'> & {
  item: SelectItem
}

const renderSelectItem = (context: SelectTemplateContext) => {
  return (
    <SelectItem
      label={context.item.label}
      ariaLabel={`${context.item.label}`}
    />
  )
}

const Select = ({
  id,
  items,
  itemTemplate,
  placeholder,
  onSelectionChange,
  label,
  variant,
  disabled,
  required = false,
  ...props
}: Props) => {
  const [droppedDown, setDroppedDown] = useState(false)

  const onDropDownChange = useCallback(
    (sender: WijmoComboBox) => {
      setDroppedDown(sender.isDroppedDown)

      if (sender.selectedItem && !sender.isDroppedDown) {
        onSelectionChange?.(sender.selectedItem)
      }
    },
    [onSelectionChange],
  )

  const onInitialized = useCallback(
    (sender: WijmoComboBox) => {
      onSelectionChange?.(sender.selectedItem)
    },
    [onSelectionChange],
  )

  const onItemSourceChanged = useCallback((sender: WijmoComboBox) => {
    sender.text = ''
  }, [])

  return (
    <div className="flex flex-col gap-sm">
      {label && id && <label htmlFor={id}>{label}</label>}
      <div className="search-bar flex items-center">
        <ComboBox
          id={id}
          displayMemberPath="label"
          placeholder={placeholder}
          itemsSource={items}
          itemsSourceChanged={onItemSourceChanged}
          wjItemTemplate={itemTemplate ?? renderSelectItem}
          initialized={onInitialized}
          isDroppedDownChanged={onDropDownChange}
          className={clsx(
            'wijmo-select',
            props.className,
            required ? 'required-select' : '',
            variant === 'md' ? 'h-[40px]' : 'h-[50px]',
            variant === 'md' ? 'text-base' : 'text-lg',
          )}
          isDisabled={disabled}
          // These two props (selectedIndex and isRequired)
          // are set to enable using the placeholder
          selectedIndex={-1}
          isRequired={false}
        />
        <SelectIcon icon={droppedDown ? 'ChevronUp' : 'ChevronDown'} />
      </div>
    </div>
  )
}

export { Select }

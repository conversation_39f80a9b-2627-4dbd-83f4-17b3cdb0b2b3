import { render } from '@/tests/react'
import { axe } from 'jest-axe'
import { SearchBar } from '.'

describe('SearchBar', () => {
  test('adheres to accessibility guidelines', async () => {
    const { container } = render(
      <SearchBar
        id="test"
        placeholder="Type to Search"
        variant="md"
        icon="Search"
        srLabel="Search"
      />,
    )
    expect(await axe(container)).toHaveNoViolations()
  })
})

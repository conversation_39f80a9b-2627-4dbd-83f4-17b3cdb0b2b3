/**
* Base Component Definition
*/

.btn-close {
  --gatx-close-button-size: 2.25rem;
  --gatx-close-button-hover-bg-color: var(--gatx-colors-light-4);
  --gatx-close-button-active-border-color: var(--gatx-colors-light-4);

  width: var(--gatx-close-button-size);
  height: var(--gatx-close-button-size);

  border-radius: var(--gatx-border-radius);
  transition: ease-out 50ms background-color;

  @media (prefers-color-scheme: dark) {
    --gatx-close-button-hover-bg-color: var(--gatx-colors-dark-5);
    --gatx-close-button-active-border-color: var(--gatx-colors-dark-5);
  }

  .icon {
    width: 100%;
    height: 100%;
  }

  &:hover,
  &:focus {
    background-color: var(--gatx-close-button-hover-bg-color);
  }

  &:focus {
    outline: none;
  }

  &:active {
    outline: thick solid var(--gatx-close-button-active-border-color);
    background-color: unset;
  }

  &:disabled {
    opacity: 0.25;
    pointer-events: none;
  }
}

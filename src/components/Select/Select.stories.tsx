import type { Meta, StoryObj } from '@storybook/react'
import { Select } from '.'

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta: Meta<typeof Select> = {
  title: 'Components/Select',
  component: Select,
  parameters: {
    layout: 'centered',
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Primary: Story = {
  name: 'Default',
  args: {
    label: 'Select Label',
    placeholder: 'Select Item',
    items: [
      { label: 'Item 1', value: 1 },
      { label: 'Item 2', value: 2 },
      { label: 'Item 3', value: 3 },
    ],
  },
}

/**
* Base component definition
*/
.nav-tabs {
  --gatx-tab-border-bottom-color: unset;
  --gatx-tab-border-radius: unset;
  --gatx-tab-gap: unset;
  --gatx-tab-bg: unset;
  --gatx-tab-color: unset;
  --gatx-tab-font-size: unset;

  --gatx-tab-active-border-bottom-color: unset;
  --gatx-tab-active-bg: unset;
  --gatx-tab-active-color: unset;

  --gatx-tab-hover-border-bottom-color: unset;
  --gatx-tab-hover-bg: unset;
  --gatx-tab-hover-color: unset;

  --gatx-tab-focus-border-bottom-color: unset;
  --gatx-tab-focus-outline-color: unset;
  --gatx-tab-focus-outline-offset: unset;
  --gatx-tab-focus-bg: unset;
  --gatx-tab-focus-color: unset;

  --gatx-nav-link-border-bottom: unset;

  display: flex;
  flex-wrap: wrap;
  gap: var(--gatx-tab-gap);

  border-bottom: 1px solid var(--gatx-tab-nav-bottom-border-color);
  list-style: none;

  .nav-link {
    padding: 8px 16px;
    border-bottom: var(--gatx-nav-link-border-bottom);
    border-radius: var(--gatx-tab-border-radius);
    background-color: var(--gatx-tab-bg);
    color: var(--gatx-tab-color);

    font-weight: var(--gatx-typography-font-weight-medium);
    font-size: var(--gatx-tab-font-size);
    font-family: var(--gatx-typography-font-family);

    text-align: center;
    text-decoration: none;
    white-space: nowrap;
    cursor: pointer;

    transition:
      color 0.15s ease-in-out,
      border-bottom 0.15s ease-in-out;

    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;

    @media (prefers-reduced-motion: reduce) {
      transition: none !important;
    }

    &.active {
      border-bottom-color: var(--gatx-tab-active-border-bottom-color);
      background-color: var(--gatx-tab-active-bg);
      color: var(--gatx-tab-active-color);
    }

    &:hover {
      border-bottom-color: var(--gatx-tab-hover-border-bottom-color);
      background-color: var(--gatx-tab-hover-bg);
      color: var(--gatx-tab-hover-color);
    }

    &:focus-visible {
      border-radius: var(--gatx-border-radius);
      border-bottom-color: var(--gatx-tab-focus-border-bottom-color);
      outline: 2px solid var(--gatx-tab-focus-outline-color);
      outline-offset: var(--gatx-tab-focus-outline-offset);
      background-color: var(--gatx-tab-focus-bg);
      color: var(--gatx-tab-focus-color);
    }
  }
}

.high-zoom {
  .nav-tabs {
    flex-wrap: nowrap;
    min-height: 46px;
    overflow-x: scroll;
  }
}

.tab-content {
  padding-top: 16px;
}

.tab-content > .tab-pane {
  display: none;
}

.tab-pane {
  height: 100%;
}

.fade:not(.show) {
  opacity: 0;
}

.fade {
  transition: opacity 0.15s linear;

  @media (prefers-reduced-motion: reduce) {
    transition: none !important;
  }
}

.tab-content > .active {
  display: block;
}

/**
* Variants - Style
*/

.tabs-underline {
  --gatx-tab-border-bottom-color: transparent;
  --gatx-nav-link-border-bottom: 3px solid var(--gatx-tab-border-bottom-color);

  --gatx-tab-gap: 2px;
  --gatx-tab-color: var(--gatx-colors-dark-5);
  --gatx-tab-font-size: var(--gatx-typography-font-size-regular);

  --gatx-tab-active-border-bottom-color: var(--gatx-color-primary);
  --gatx-tab-active-color: var(--gatx-colors-dark-8);

  --gatx-tab-hover-border-bottom-color: var(--gatx-colors-light-7);
  --gatx-tab-hover-color: var(--gatx-colors-dark-8);

  --gatx-tab-focus-border-bottom-color: transparent;
  --gatx-tab-focus-outline-color: var(--gatx-colors-light-7);
  --gatx-tab-focus-outline-offset: 0px;
  --gatx-tab-focus-color: var(--gatx-colors-dark-8);

  --gatx-tab-nav-bottom-border-color: var(--gatx-colors-light-7);

  @media (prefers-color-scheme: dark) {
    --gatx-tab-color: var(--gatx-colors-dark-3);

    --gatx-tab-active-color: var(--gatx-colors-light-1);

    --gatx-tab-hover-border-bottom-color: var(--gatx-colors-dark-3);
    --gatx-tab-hover-color: var(--gatx-colors-light-1);

    --gatx-tab-focus-color: var(--gatx-colors-light-1);
    --gatx-tab-focus-outline-color: var(--gatx-colors-dark-3);

    --gatx-tab-nav-bottom-border-color: var(--gatx-colors-dark-6);
  }
}

.tabs-pills {
  --gatx-tab-border-radius: var(--gatx-border-radius);
  --gatx-tab-gap: 8px;
  --gatx-tab-bg: var(--gatx-colors-light-7);
  --gatx-tab-color: var(--gatx-typography-color-text);
  --gatx-tab-font-size: var(--gatx-typography-font-size-xs);

  --gatx-tab-active-bg: var(--gatx-colors-primary-7);
  --gatx-tab-active-color: var(--gatx-colors-light-1);

  --gatx-tab-hover-bg: var(--gatx-colors-light-8);
  --gatx-tab-hover-color: var(--gatx-typography-color-text);

  --gatx-tab-focus-bg: var(--gatx-colors-light-7);
  --gatx-tab-focus-color: var(--gatx-typography-color-text);
  --gatx-tab-focus-outline-color: var(--gatx-colors-light-8);

  --gatx-tab-nav-bottom-border-color: unset;
  --gatx-nav-link-border-bottom: none;

  @media (prefers-color-scheme: dark) {
    --gatx-tab-bg: var(--gatx-colors-dark-9);

    --gatx-tab-active-bg: var(--gatx-colors-primary-5);

    --gatx-tab-hover-bg: var(--gatx-colors-dark-5);

    --gatx-tab-focus-bg: var(--gatx-colors-dark-5);
    --gatx-tab-focus-outline-color: var(--gatx-colors-dark-5);
    --gatx-tab-focus-outline-offset: 2px;
  }
}

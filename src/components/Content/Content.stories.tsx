import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { Heading, Landmark } from '.'

const meta: Meta<typeof Landmark> = {
  title: 'Components/Content',
  component: Landmark,
  subcomponents: { Heading: Heading as React.ComponentType<unknown> },
}

export default meta
type Story = StoryObj<typeof meta>

export const LandmarkMain: Story = {
  name: 'Landmark: Main',
  parameters: {
    docs: {
      description: {
        story: `The Landmark's semantic element can be specified, which allows
          us to, e.g., render the top-level Landmark for a page as a "main".`,
      },
    },
  },
  args: {
    as: 'main',
    children: <Heading text="This will render semantically as an H1" />,
  },
}

export const LandmarkRegion: Story = {
  name: 'Landmark: Section',
  parameters: {
    docs: {
      description: {
        story: `A Landmark will render as a section element by default. The
          initial heading level of the component can be specified via props. Any
          Heading element rendered within the component will automatically
          inherit the appropriate heading level, based on that initial level.`,
      },
    },
  },
  args: {
    level: 4,
    children: <Heading text="This will render semantically as an h4" />,
  },
}

export const LandmarkNested: Story = {
  name: 'Landmark: Nesting',
  parameters: {
    docs: {
      description: {
        story: `Nesting a Landmark inside of another Landmark will automatically
          increment the heading level, ensuring that header elements produced
          inside of the new sub-region have a higher level than the parent.`,
      },
    },
  },
  args: {
    children: (
      <>
        <Heading text="This will render semantically as an H1" />
        <Landmark>
          <Heading text="And this will render semantically as an H2" />
        </Landmark>
      </>
    ),
  },
}

export const LandmarkVirtual: Story = {
  name: 'Landmark: Virtual',
  parameters: {
    docs: {
      description: {
        story: `In the event that a suitable organization element already exists
          on the page, the "virtual" prop allows for the use of a Landmark to
          update the heading context without inserting a new element. This is
          particularly useful when working with components that can adopt the
          appropriate role for a landmark without the need for a separate
          container.`,
      },
    },
  },
  args: {
    virtual: true,
    children: <Heading text="This H1 has no parent element" />,
  },
}

export const HeadingStyles: Story = {
  name: 'Heading: Styling',
  parameters: {
    docs: {
      description: {
        story: `There are six heading styles intended for use with the Heading
          component. They're expected to be used in lieu of element-specific
          style rules for the various levels of header. For more information,
          see the [Typography](/docs/style-guide-typography--docs#heading-classes)
          documentation.`,
      },
    },
  },
  args: {
    children: (
      <Heading
        className="text-title-lg"
        text="This will render in a large font"
      />
    ),
  },
}

export const HeadingOverride: Story = {
  name: 'Heading: Level Override',
  parameters: {
    docs: {
      description: {
        story: `While Heading levels will be automatically computed by default,
          a level can be manually supplied for cases where the information
          hierarchy needs to be represented differently.`,
      },
    },
  },
  args: {
    children: (
      <Heading level={3} text="This will render semantically as an H3" />
    ),
  },
}

export const HeadingScreenreaderOnly: Story = {
  name: 'Heading: Screenreader Only',
  parameters: {
    docs: {
      description: {
        story: `Used to provide semantic meaning to a section of content and
          page organization for screenreaders or other assistive technology
          without visually displaying the heading. This is useful for tab
          panels.`,
      },
    },
  },
  args: {
    children: (
      <>
        <Heading screenreaderOnly text="This is a Heading" />
        <p>
          This content section has an invisible header that will announce
          &quot;This is a Heading&quot; to any assistive tooling.
        </p>
      </>
    ),
  },
}

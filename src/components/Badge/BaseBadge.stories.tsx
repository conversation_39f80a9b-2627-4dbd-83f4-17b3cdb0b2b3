import type { Meta } from '@storybook/react'
import { BaseBadge } from './BaseBadge'

const meta: Meta<typeof BaseBadge> = {
  title: 'Components/Badge/BaseBadge',
  component: BaseBadge,
  parameters: {
    layout: 'centered',
  },
}

export default meta

export const Default: Meta<typeof BaseBadge> = {
  component: BaseBadge,
  args: {
    label: 'Badge',
  },
}

/**
 * The `BaseBadge` supports six different variants: `primary`, `outline`, `info`, `warning`, `error`, and `success`.
 */
export const Variants: Meta<typeof BaseBadge> = {
  argTypes: {
    variant: { control: false },
  },
  render: ({ label, ...args }) => (
    <div className="flex gap-sm">
      {(
        [
          'primary',
          'outline',
          'info',
          'warning',
          'error',
          'success',
          'disabled',
        ] as const
      ).map((variant) => (
        <BaseBadge
          {...args}
          key={variant}
          label={label || variant.charAt(0).toUpperCase() + variant.slice(1)}
          variant={variant}
        />
      ))}
    </div>
  ),
}

/**
 * The `BaseBadge` supports three different sizes: `sm`, `md`, and `lg`.
 *
 * The `sm` and `md` sizes do not have a color-scheme aware palette, so they should be used carefully to ensure AA-compliant contrast on every variant.
 */
export const Sizes: Meta<typeof BaseBadge> = {
  argTypes: {
    size: { control: false },
  },
  render: ({ label, ...args }) => (
    <div className="grid grid-cols-3 gap-y-sm gap-x-2xl items-center">
      <span>sm</span>
      <span>md</span>
      <span>lg</span>
      {(['sm', 'md', 'lg'] as const).map((size) => (
        <BaseBadge key={size} {...args} label={label || 'Badge'} size={size} />
      ))}
    </div>
  ),
}

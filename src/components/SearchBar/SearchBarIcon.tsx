import clsx from 'clsx'
import { Spinner } from 'react-bootstrap'
import * as Icons from 'react-bootstrap-icons'

type IconName = keyof typeof Icons

type Props = {
  icon: string
  isLoading?: boolean
  isDisabled?: boolean
}

const SearchBarIcon = ({ icon, isLoading, isDisabled }: Props) => {
  const IconComponent = icon ? Icons[icon as IconName] : null

  return !isLoading ? (
    IconComponent && (
      <IconComponent
        aria-hidden
        className={clsx(
          'search-bar-icon',
          isDisabled ? 'search-bar-icon-disabled' : '',
        )}
      />
    )
  ) : (
    <Spinner
      className="search-bar-spinner "
      size="sm"
      animation="border"
      role="status"
    />
  )
}

export { SearchBarIcon }

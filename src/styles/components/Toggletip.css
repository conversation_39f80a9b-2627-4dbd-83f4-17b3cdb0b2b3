/**
* Base Component Definition
*/

.btn-toggletip.btn-primary.btn {
  --gatx-btn-bg: transparent;
  --gatx-btn-border-color: none;
  --gatx-btn-color: var(--gatx-colors-light-9);
  --gatx-btn-border-radius: 50%;
  --gatx-btn-padding: 0;
  --gatx-btn-font-size: 1rem;

  width: 1rem;
  height: 1rem;
  margin: 0;
  border: none;
  background-color: var(--gatx-btn-bg);

  @media (prefers-color-scheme: dark) {
    --gatx-btn-color: var(--gatx-colors-dark-3);
  }

  .icon {
    width: 1rem;
    height: 1rem;
    margin: 0;
    padding: 0;
    color: var(--gatx-btn-color);
  }

  &:hover {
    --gatx-btn-color: var(--gatx-colors-dark-8);

    @media (prefers-color-scheme: dark) {
      --gatx-btn-color: var(--gatx-colors-light-1);
    }
  }

  &:focus {
    --gatx-btn-color: var(--gatx-colors-dark-8);
    outline: none;

    @media (prefers-color-scheme: dark) {
      --gatx-btn-color: var(--gatx-colors-light-1);
    }
  }
}

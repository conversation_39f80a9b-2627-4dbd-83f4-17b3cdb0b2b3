/**
 Base Component
 */

.resource-sidebar {
  --gatx-resource-sidebar-bg: var(--gatx-colors-primary-7);
  --gatx-resource-sidebar-bg-hover: rgba(0, 0, 0, 0.25);

  --gatx-resource-sidebar-icon-color: var(--gatx-colors-light-1);
  --gatx-resource-sidebar-text-color: var(--gatx-colors-primary-1);
  --gatx-resource-sidebar-text-hover-color: var(--gatx-colors-light-1);
  --gatx-resource-sidebar-header-title-text-color: var(--gatx-colors-light-1);

  --gatx-resource-sidebar-focus: 0px 0px 0px 4px rgba(71, 179, 255, 0.4);

  --gatx-resource-sidebar-border-color: var(--gatx-colors-primary-5);

  @media (prefers-color-scheme: dark) {
    --gatx-resource-sidebar-bg: var(--gatx-colors-dark-9);

    --gatx-resource-sidebar-text-color: var(--gatx-colors-dark-2);
    --gatx-resource-sidebar-text-hover-color: var(--gatx-colors-light-1);

    --gatx-resource-sidebar-border-color: var(--gatx-colors-dark-7);
    --gatx-resource-sidebar-header-title-text-color: var(--gatx-colors-dark-1);
  }

  display: flex;
  flex-direction: column;
  height: 100%;

  background-color: var(--gatx-resource-sidebar-bg);
  box-shadow: #00000015 0px 8px 16px 0;
  font-family: var(--gatx-typography-font-family);
}

.resource-sidebar-header {
  display: flex;
  align-items: center;
  justify-content: start;
  height: 70px;
  margin: 0;
  gap: var(--gatx-spacing-xs);

  background-color: var(--gatx-resource-sidebar-bg);
}

.resource-sidebar-header-title span {
  color: var(--gatx-resource-sidebar-header-title-text-color);
  font-weight: var(--gatx-typography-font-weight-medium);
  font-size: var(--gatx-typography-font-size-lead);
}

.resource-sidebar-section {
  padding: var(--gatx-spacing-sm) 0px;

  &:not(:last-of-type) {
    border-bottom: 1px solid var(--gatx-resource-sidebar-border-color);
  }
}

.resource-sidebar-content {
  overflow-x: hidden;
  overflow-y: auto;
}

.resource-sidebar-item {
  height: 40px;
  margin: var(--gatx-spacing-sm);
  padding: 6px 12px;
  border-radius: var(--gatx-border-radius);
  color: var(--gatx-resource-sidebar-text-color);
  text-align: left;
  text-decoration: none;

  &:hover {
    background-color: var(--gatx-resource-sidebar-bg-hover);
    color: var(--gatx-resource-sidebar-text-hover-color);
  }

  &:focus {
    outline: none;
    background-color: var(--gatx-resource-sidebar-bg-hover);
    box-shadow: var(--gatx-resource-sidebar-focus);
    color: var(--gatx-resource-sidebar-text-hover-color);
  }
}

.resource-sidebar-icon {
  color: var(--gatx-resource-sidebar-icon-color);
}

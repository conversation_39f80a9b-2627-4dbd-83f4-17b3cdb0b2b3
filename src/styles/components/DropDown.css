.dropdown-menu {
  --gatx-dropdown-text-color: unset;
  --gatx-dropdown-hover-color: unset;
  --gatx-dropdown-active-color: unset;
  --gatx-dropdown-disabled-color: unset;
  --gatx-dropdown-link-hover-bg: unset;
  --gatx-dropdown-link-active-bg: unset;
  --gatx-dropdown-box-shadow: 0px 2px 4px 0px var(--gatx-colors-dark-2);

  --gatx-dropdown-bg-color: var(--gatx-colors-light-2);
  --gatx-dropdown-border-color: var(--gatx-colors-light-7);
  --gatx-dropdown-border-width: 1px;

  --gatx-dropdown-chevron-color: var(--gatx-colors-dark-9);
  --gatx-dropdown-divider-bg: unset;
  --gatx-dropdown-zindex: 1000;
  --gatx-dropdown-min-width: 200px;
  --gatx-dropdown-padding-x: var(--gatx-spacing-xs);
  --gatx-dropdown-padding-y: var(--gatx-spacing-xs);
  --gatx-dropdown-spacer: var(--gatx-spacing-xs);
  --gatx-dropdown-font-size: var(--gatx-typography-font-size-regular);
  --gatx-dropdown-divider-margin-y: var(--gatx-spacing-xs);
  --gatx-dropdown-item-padding-x: var(--gatx-spacing-xs);
  --gatx-dropdown-item-padding-y: var(--gatx-spacing-xs);
  --gatx-dropdown-header-color: var(--gatx-colors-dark-3);
  --gatx-dropdown-header-padding-x: var(--gatx-spacing-sm);
  --gatx-dropdown-header-padding-y: var(--gatx-spacing-xs);

  @media (prefers-color-scheme: dark) {
    --gatx-dropdown-text-color: var(--gatx-typography-color-text-subtle);
    --gatx-dropdown-bg-color: var(--gatx-colors-dark-9);
    --gatx-dropdown-border-color: var(--gatx-colors-dark-10);
    --gatx-dropdown-chevron-color: var(--gatx-colors-light-2);
    --gatx-dropdown-box-shadow: 0px 2px 4px 0px var(--gatx-colors-dark-6);
  }

  display: none;
  z-index: var(--gatx-dropdown-zindex);
  position: absolute;
  min-width: var(--gatx-dropdown-min-width);
  max-height: 250px;
  margin: 0;
  margin-top: var(--gatx-dropdown-spacer);
  overflow-y: auto;
  border: var(--gatx-dropdown-border-width) solid
    var(--gatx-dropdown-border-color);
  border-radius: var(--gatx-border-radius);

  background-clip: padding-box;
  background-color: var(--gatx-dropdown-bg-color);
  box-shadow: var(--gatx-dropdown-box-shadow);
  color: var(--gatx-dropdown-text-color);
  font-size: var(--gatx-dropdown-font-size);
  list-style: none;
  text-align: left;
}

.dropdown-toggle,
.dropdown-toggle.btn.show {
  border: none;
  background-color: var(--gatx-dropdown-bg-color);
  color: unset;

  &:focus,
  &:hover,
  &:active {
    background-color: var(--gatx-dropdown-bg-color);
    color: unset;
  }

  @media (prefers-color-scheme: dark) {
    border: none;
    background-color: var(--gatx-dropdown-bg-color);
    color: unset;
    &:focus,
    &:hover,
    &:active {
      background-color: var(--gatx-dropdown-bg-color);
      color: unset;
    }
  }
}

.dropdown-menu-start {
  --gatx-position: start;
}

.dropdown-divider {
  height: 0;
  margin: var(--gatx-dropdown-divider-margin-y) 0;
  overflow: hidden;
  border-top: 1px solid var(--gatx-dropdown-divider-bg);
  opacity: 1;
}

.dropdown-item {
  --gatx-dropdown-item-focus-color: var(--gatx-colors-dark-8);
  --gatx-dropdown-item-focus-bg: var(--gatx-colors-light-3);

  @media (prefers-color-scheme: dark) {
    --gatx-dropdown-item-focus-color: var(--gatx-colors-light-1);
    --gatx-dropdown-item-focus-bg: var(--gatx-colors-dark-10);
  }

  width: 100%;
  padding: var(--gatx-dropdown-item-padding-y)
    var(--gatx-dropdown-item-padding-x);
  padding: var(--gatx-spacing-sm);
  border-radius: var(--gatx-border-radius);
  background-color: initial;
  color: var(--gatx-dropdown-text-color);
  font-weight: var(--gatx-typography-font-weight-regular);
  text-decoration: none;

  &:hover {
    background: var(--gatx-dropdown-item-focus-bg);
    color: var(--gatx-dropdown-item-focus-color);
  }

  &:focus {
    box-sizing: border-box;
    border-radius: var(--gatx-border-radius);
    outline: 4px solid
      color-mix(in srgb, var(--gatx-colors-primary-7), transparent 40%);
    outline-offset: -4px;
    background: var(--gatx-dropdown-item-focus-bg);
    color: var(--gatx-dropdown-item-focus-color);

    @media (prefers-color-scheme: dark) {
      outline: 4px solid
        color-mix(in srgb, var(--gatx-colors-secondary-5), transparent 40%);
    }
  }
}

.dropdown-item-icon {
  margin-right: 8px;
}

.dropdown-menu.show {
  display: flex;
  flex-direction: column;
}

.dropdown-header {
  display: block;
  margin-bottom: 0;
  padding: var(--gatx-dropdown-header-padding-y)
    var(--gatx-dropdown-header-padding-x);
  color: var(--gatx-dropdown-header-color);
  font-size: var(--gatx-typography-line-height-title);
  white-space: nowrap;
}

.dropdown-item-text {
  display: block;
  padding: var(--gatx-dropdown-item-padding-y)
    var(--gatx-dropdown-item-padding-x);
  color: var(--gatx-dropdown-text-color);
}

.dropdown-item * {
  display: inline;
}

.dropdown-item.btn-primary {
  border: none;
}

.dropdown-display-toggle {
  &:after {
    display: none;
  }

  .dropdown-icon {
    margin-left: 8px;
  }
}

.dropdown-display-toggle.show {
  &:after {
    transform: translateY(25%) rotate(-135deg);
  }
}

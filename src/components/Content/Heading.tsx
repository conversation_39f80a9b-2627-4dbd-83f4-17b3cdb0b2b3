import clsx from 'clsx'
import { createElement, HTMLProps, use, useContext } from 'react'
import { HeadingIdContext, HeadingLevelContext } from './Context'

type Props = HTMLProps<HTMLDivElement> & {
  /** The heading level that should be applied (i.e. h2 = 2) */
  level?: 'auto' | number
  /** The heading's text. */
  text?: string
  /** Should the heading be visible only to screen readers? */
  screenreaderOnly?: boolean
}

/**
 * The Heading component is used to provide an accessible header for a section
 * of content.
 *
 * It does not provide any styling, only the semantic meaning of a heading.
 * Refer to our TailwindCSS configuration for the heading font size definitions
 * that should be used to apply appropriate styling.
 */
export const Heading = ({
  level = 'auto',
  screenreaderOnly = false,
  text,
  children,
  ...props
}: Props) => {
  const id = useContext(HeadingIdContext)

  const headingLevel = level === 'auto' ? use(HeadingLevelContext) : level

  if (headingLevel <= 0) {
    throw new Error(
      'To use auto heading levels, wrap your Heading in a Region.',
    )
  }

  if (!!text && !!children) {
    throw new Error(
      'Heading content should be provided via the `text` prop or via the component children; not both.',
    )
  }

  let headingElement = `h${headingLevel}` as keyof HTMLElementTagNameMap
  const headingProps = {
    id,
    children: text ?? children,
    ...props,
    className: clsx(props.className, screenreaderOnly && 'sr-only'),
  }

  if (headingLevel > 6) {
    headingElement = 'div'
    headingProps.role = 'heading'
    headingProps['aria-level'] = headingLevel
  }

  return createElement(headingElement, headingProps)
}

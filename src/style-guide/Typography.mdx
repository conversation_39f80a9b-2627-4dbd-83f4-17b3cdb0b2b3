{/* organize-imports-ignore */}
import { Meta, Unstyled } from '@storybook/blocks'

<Meta title="Style Guide/Typography" />

# Typography

Uses a native font stack that selects the best font-family for each OS and device, and font size is a default root of 16px.

## Font Weights

<Unstyled>
  <div className="docs-story">
    <p style={{ fontWeight: 'var(--gatx-typography-font-weight-light)' }}>
      300 (light)
    </p>
    <p style={{ fontWeight: 'var(--gatx-typography-font-weight-regular)' }}>
      400 (regular)
    </p>
    <p style={{ fontWeight: 'var(--gatx-typography-font-weight-medium)' }}>
      500 (medium)
    </p>
    <p style={{ fontWeight: 'var(--gatx-typography-font-weight-bold)' }}>
      700 (bold)
    </p>
  </div>
</Unstyled>

## Heading Classes

Due to lack of static text, our heading levels, while used semantically, are not differentiated visually.

A Heading component is used, and defaults to a font size of 16px or 1rem. All headings should use a line height of 1.2.

Stylistically we will instead use title classes to define different text styles outside of the body copy.

<Unstyled>
  <div className="docs-story">
    <div className="text-title-xl">text-title-xl</div>
    <div className="text-title-lg">text-title-lg</div>
    <div className="text-title-md">text-title-md</div>
    <div className="text-title-rg">text-title-rg</div>
    <div className="text-title-sm">text-title-sm</div>
    <div className="text-title-xs">text-title-xs</div>
  </div>
</Unstyled>

## Body Text

All body text should use a line height of 1.5

<Unstyled>
  <div className="docs-story">
    <p style={{ fontSize: 'var(--gatx-typography-font-size-xs)' }}>
      This is some paragraph text, using var(--gatx-typography-font-size-xs).
    </p>
    <p style={{ fontSize: 'var(--gatx-typography-font-size-sm)' }}>
      This is some paragraph text, using var(--gatx-typography-font-size-sm).
    </p>
    <p style={{ fontSize: 'var(--gatx-typography-font-size-regular)' }}>
      This is some paragraph text, using
      var(--gatx-typography-font-size-regular).
    </p>
    <p style={{ fontSize: 'var(--gatx-typography-font-size-sm-lead)' }}>
      This is some paragraph text, using
      var(--gatx-typography-font-size-sm-lead).
    </p>
    <p style={{ fontSize: 'var(--gatx-typography-font-size-lead)' }}>
      This is some paragraph text, using var(--gatx-typography-font-size-lead).
    </p>
  </div>
</Unstyled>

import { <PERSON>a, StoryObj } from '@storybook/react'
import { ArrowRepeat } from 'react-bootstrap-icons'
import { Banner } from '.'

const meta: Meta<typeof Banner> = {
  title: 'Components/Banner',
  component: Banner,
}

export default meta
type Story = StoryObj<typeof meta>

export const Warning: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Banner to communicate warning measures.',
      },
    },
  },
  args: {
    variant: 'warning',
    title: 'Offline',
    text: "You're currently offline, all work will be stored on your device until you are back online.",
    icon: 'CloudSlashFill',
  },
}

export const Success: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Banner to communite online connectivity, and unsaved form changes',
      },
    },
  },
  args: {
    variant: 'success',
    title: 'Online',
    text: 'You are back online. Please save your local changes to the server.',
    icon: 'CloudFill',
    children: (
      <button>
        <ArrowRepeat className="icon" role="presentation" /> Save Changes
      </button>
    ),
  },
}

import typescript from '@rollup/plugin-typescript'
import react from '@vitejs/plugin-react'
import { globSync } from 'glob'
import fs from 'node:fs'
import path, { dirname } from 'node:path'
import { fileURLToPath } from 'node:url'

import preserveDirectives from 'rollup-preserve-directives'
import { defineConfig, Plugin } from 'vite'
import packageJson from './package.json'
import postcssGATXPlatformOneCommon from './src/postcss'

const __dirname = dirname(fileURLToPath(import.meta.url))

const components = globSync([
  path.join(__dirname, 'src/components/*/index.tsx'),
  path.join(__dirname, 'src/components/*/index.ts'),
]).reduce(
  (entries, file) => {
    const componentName = path.basename(path.dirname(file))
    entries[`components/${componentName}`] = file
    return entries
  },
  {} as Record<string, string>,
)

const stores = globSync([
  path.join(__dirname, 'src/stores/*/index.tsx'),
  path.join(__dirname, 'src/stores/*/index.ts'),
]).reduce(
  (entries, file) => {
    const storeName = path.basename(path.dirname(file))
    entries[`stores/${storeName}`] = file
    return entries
  },
  {} as Record<string, string>,
)

const icons = globSync([path.join(__dirname, 'src/icons/*.tsx')]).reduce(
  (entries, file) => {
    const { name } = path.parse(path.basename(file))
    entries[`icons/${name}`] = file
    return entries
  },
  {} as Record<string, string>,
)

const BUILD_DIR = 'dist'

type Exports = typeof packageJson.exports
type Export = Exports[keyof Exports] | string

function cleanExport<E extends Export>(entry: E): E {
  if (typeof entry === 'string') {
    return entry.replace(`${BUILD_DIR}/`, '') as E
  }

  return Object.fromEntries(
    Object.entries(entry).map(([key, value]) => [key, cleanExport(value)]),
  ) as E
}

function prepareExports(exports: Exports): Exports {
  return Object.fromEntries(
    Object.entries(exports).map(([key, value]) => [key, cleanExport(value)]),
  ) as Exports
}

const copyPackageJson = (): Plugin => {
  return {
    name: 'copy-package-json',
    closeBundle: {
      sequential: true,
      order: 'pre',
      handler() {
        fs.writeFileSync(
          `./${BUILD_DIR}/package.json`,
          JSON.stringify(
            {
              ...packageJson,
              exports: prepareExports(packageJson.exports),
            },
            null,
            2,
          ),
        )
      },
    },
  }
}

const copyTailwindConfig = (): Plugin => {
  return {
    name: 'copy-tailwind-config',
    closeBundle: {
      sequential: true,
      order: 'pre',
      handler() {
        fs.copyFileSync(
          path.join(__dirname, 'src/styles/tailwind.css'),
          path.join(__dirname, `${BUILD_DIR}/tailwind.css`),
        )
      },
    },
  }
}

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': '/src',
    },
  },
  css: {
    postcss: {
      plugins: [postcssGATXPlatformOneCommon()],
    },
  },
  build: {
    lib: {
      entry: {
        ...components,
        ...stores,
        ...icons,
        auth: './src/auth/index.ts',
        'auth-server': './src/auth/next-auth.ts',
        wijmo: './src/wijmo.tsx',
        index: './src/index.tsx',
        styles: './src/styles/index.css',
        postcss: './src/postcss.ts',
      },
      formats: ['es', 'cjs'],
    },
    rollupOptions: {
      plugins: [
        preserveDirectives(),
        typescript({
          include: ['**/*.ts', '**/*.tsx'],
          exclude: [
            '**/*.stories.tsx',
            '**/*.stories.ts',
            '**/*.test.ts',
            '**/*.test.tsx',
            '**/*.config.ts',
            '**/*.config.js',
            '**/*.config.mjs',
            '**/*.mdx',
            '**/*.md',
            '**/*.json',
            '**/*.jsonc',
            'jest.**.ts',
          ],
        }),
        copyPackageJson(),
        copyTailwindConfig(),
      ],
      external: [...Object.keys(packageJson.peerDependencies), 'next/server'],
      output: {
        preserveModules: false,
        dir: BUILD_DIR,
      },
    },
    sourcemap: true,
    emptyOutDir: true,
    cssCodeSplit: true,
  },
})

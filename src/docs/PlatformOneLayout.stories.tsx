import { SiteHeader } from '@/components/SiteHeader'
import type { Meta, StoryObj } from '@storybook/react'
import { PlatformOneLayout } from './PlatformOneLayout'

const meta: Meta<typeof SiteHeader> = {
  title: 'Layouts/PlatformOne',
  component: PlatformOneLayout,
  parameters: {
    layout: 'padded',
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    application: 'PlatformOne',
  },
}

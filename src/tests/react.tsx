import {
  render,
  renderHook,
  RenderHookOptions,
  RenderOptions,
} from '@testing-library/react'
import fs from 'fs'
import {
  ComponentProps,
  Fragment,
  JSXElementConstructor,
  ReactElement,
  ReactNode,
} from 'react'

function getFiles(dir: string): string[] {
  return fs
    .readdirSync(dir)
    .filter((file) => file.endsWith('.css'))
    .flatMap((file) => {
      const name = `${dir}/${file}`

      if (fs.statSync(name).isDirectory()) {
        return getFiles(name)
      } else {
        return name
      }
    })
}

const files = getFiles(`${__dirname}/../../dist`)
const css = files.map((file) => {
  const text = fs
    .readFileSync(file, 'utf8')
    /**
     * Removes @bottom-center {*}, @bottom-left {*} and @bottom-right {*}
     * as it's not currently supported by JSDOM.
     */
    .replaceAll(/@bottom-(center|left|right)\s*\{[\s\S]*?\}/g, '')
  return text
})

type Props = {
  children: ReactNode
}

/**
 * Prepend the application's global css in a <style> element.
 *
 * This wrapper is meant to be used in component testing where CSS rules must
 * be evaluated in order for the component to function. Note that because the
 * CSS is built from NextJS's compiled static assets, this wrapper only works if
 * you're either running your tests in a local environment where assets have
 * already been compiled, or you explicitly run a `next build` prior to
 * executing the test suite.
 */
function WithStyles(
  Component: RenderOptions['wrapper'] = Fragment,
  props: Record<string, unknown> = {},
) {
  return function ComponentWithStyles({ children }: Props) {
    return (
      <>
        <style>{css.join()}</style>
        <Component {...props}>{children}</Component>
      </>
    )
  }
}

function WithWrapper(
  Component: JSXElementConstructor<any> = Fragment,
  props: Record<string, unknown> = {},
) {
  return function ComponentWithWrapper({ children }: Props) {
    return <Component {...props}>{children}</Component>
  }
}

type Options<W extends JSXElementConstructor<any>, O> = Omit<O, 'wrapper'> &
  (
    | { wrapper?: never }
    | { wrapper: W; wrapperProps: Omit<ComponentProps<W>, 'children'> }
  )

function styledRender<W extends JSXElementConstructor<any>>(
  ui: ReactElement<any>,
  options: Options<W, RenderOptions> = {},
) {
  const wrapperProps = 'wrapperProps' in options ? options.wrapperProps : {}

  return render(ui, {
    ...options,
    wrapper: WithStyles(options.wrapper, wrapperProps),
  })
}

function wrappedRenderHook<R, P, W extends JSXElementConstructor<any>>(
  hook: (p: P) => R,
  options: Options<W, RenderHookOptions<P>> = {},
) {
  const wrapperProps = 'wrapperProps' in options ? options.wrapperProps : {}

  return renderHook(hook, {
    ...options,
    wrapper: WithWrapper(options.wrapper, wrapperProps),
  })
}

export * from '@testing-library/react'
export { styledRender as render, wrappedRenderHook as renderHook }

import { render, screen } from '@/tests/react'
import userEvent from '@testing-library/user-event'
import { axe } from 'jest-axe'
import { ChoiceGroup } from '.'

describe('ChoiceGroup', () => {
  test('renders a group of multiselect chips with provided props', () => {
    const el = render(
      <ChoiceGroup
        multiSelect
        value={[]}
        onChange={() => {
          console.log('--')
        }}
        size="small"
        label="Multi Select"
        options={[{ label: 'Chocolate', value: 'c' }]}
      />,
    )

    const group = el.getByRole('group', { name: 'Multi Select' })
    const chip = el.getByRole('checkbox', { name: 'Chocolate' })

    expect(group).toContainElement(chip)
  })

  test('renders a group of single select chips with provided props', () => {
    const el = render(
      <ChoiceGroup
        multiSelect={false}
        value={''}
        onChange={() => {
          console.log('--')
        }}
        size="small"
        label="Single Select"
        options={[{ label: 'Chocolate', value: 'c' }]}
      />,
    )

    const group = el.getByRole('radiogroup', { name: 'Single Select' })
    const chip = el.getByRole('radio', { name: 'Chocolate' })

    expect(group).toContainElement(chip)
  })

  test('handles change events on multi select chips', async () => {
    const handleChange = jest.fn()

    const el = render(
      <ChoiceGroup
        multiSelect
        value={[]}
        onChange={handleChange}
        size="small"
        label="Multi Select"
        options={[{ label: 'Chocolate', value: 'c' }]}
      />,
    )
    const chip = el.getByRole('checkbox', { name: 'Chocolate' })
    await userEvent.click(chip)
    expect(handleChange).toHaveBeenCalledWith(['c'])
  })

  test('handles chaneg events on single select chips', async () => {
    const handleChange = jest.fn()

    render(
      <ChoiceGroup
        multiSelect={false}
        value={''}
        onChange={handleChange}
        size="small"
        label="Single Select"
        options={[{ label: 'Chocolate', value: 'c' }]}
      />,
    )

    const chip = screen.getByRole('radio', { name: 'Chocolate' })
    await userEvent.click(chip)
    expect(handleChange).toHaveBeenCalledWith('c')
  })

  test('does not handle change events when disabled', async () => {
    const handleChange = jest.fn()

    render(
      <ChoiceGroup
        multiSelect={false}
        value={''}
        onChange={handleChange}
        disabled
        size="small"
        label="Single Select"
        options={[{ label: 'Chocolate', value: 'c' }]}
      />,
    )

    const chip = screen.getByRole('radio', { name: 'Chocolate' })
    await userEvent.click(chip)
    expect(handleChange).not.toHaveBeenCalledWith('c')
  })

  test('adheres to accessibility guidelines', async () => {
    const { container } = render(
      <ChoiceGroup
        multiSelect={false}
        value={''}
        onChange={() => {
          console.log('--')
        }}
        size="small"
        label="Single Select"
        options={[{ label: 'Chocolate', value: 'c' }]}
      />,
    )
    expect(await axe(container)).toHaveNoViolations()
  })
})

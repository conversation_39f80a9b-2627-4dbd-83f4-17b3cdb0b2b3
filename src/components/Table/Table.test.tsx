import { render } from '@/tests/react'
import { axe } from 'jest-axe'
import { Table } from '.'

describe('Table', () => {
  test('adheres to accessibility guidelines with column headers', async () => {
    const { container } = render(
      <Table
        columnHeaders={['Name', 'Date']}
        rows={[
          ['Value 1', '2024-10-13'],
          ['Value 2', '2024-11-19'],
        ]}
      />,
    )
    expect(await axe(container)).toHaveNoViolations()
  })

  test('adheres to accessibility guidelines with row headers', async () => {
    const { container } = render(
      <Table
        hasRowHeaders
        rows={[
          ['Stenciled Class/Car Style', 'DOT 111A100-W-1'],
          ['Product Code', 'GSL0016'],
          ['Certificate of Construction', 'L986099A'],
          ['Mechanical Designation', 'T - Tank'],
        ]}
      />,
    )
    expect(await axe(container)).toHaveNoViolations()
  })
})

{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable", "esnext", "webworker"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": false, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "incremental": true, "declaration": true, "declarationDir": "./dist/types", "outDir": "./dist", "paths": {"@/*": ["./src/*"], "@": ["./src/index.tsx"]}, "noErrorTruncation": true}, "files": ["./src/auth/next-auth.d.ts"], "include": ["**/*.mdx", "**/*.ts", "**/*.tsx", "*.config.mjs", "*.config.js", "*.config.ts", "scripts/**/*.js"], "exclude": ["node_modules", "storybook-static", "dist"]}
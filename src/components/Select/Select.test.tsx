import { render, waitFor } from '@/tests/react'
import { axe } from 'jest-axe'
import { Select } from '.'

describe('Select', () => {
  test('adheres to accessibility guidelines', async () => {
    const { container } = render(
      <Select
        id="test"
        placeholder="Type to Search"
        variant="md"
        items={[
          { label: 'Test 1', value: '1' },
          { label: 'Test 2', value: '2' },
          { label: 'Test 3', value: '3' },
        ]}
      />,
    )

    await waitFor(async () => expect(await axe(container)).toHaveNoViolations())
  })
})

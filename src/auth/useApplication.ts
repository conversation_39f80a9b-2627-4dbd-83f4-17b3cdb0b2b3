import { PlatformOneContext } from '@'
import { useContext } from 'react'

/**
 * Returns data relating to the current application. Leveraged by other hooks
 * primarily to throw an error if the context hasn't been initialized due to a
 * missing provider element.
 */
export function useApplication() {
  const application = useContext(PlatformOneContext)

  if (!application) {
    throw new Error(
      'Application user not available. Did you forget to wrap your component in a <PlatformOneProvider>?',
    )
  }

  return application
}

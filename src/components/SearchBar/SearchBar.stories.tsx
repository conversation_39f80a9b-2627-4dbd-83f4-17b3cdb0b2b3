import { ItemTemplateContext } from '@mescius/wijmo.react.input'
import type { Meta, StoryObj } from '@storybook/react'

import { WijmoLicenseProvider } from '@/wijmo'
import * as Icon from 'react-bootstrap-icons'
import { SearchBar, SearchBarItem } from '.'

type CustomItem = {
  id: number
  text: string
}

const customItems = [
  { id: 1, text: 'GATX059671' },
  { id: 2, text: 'GATX069523' },
  { id: 3, text: 'GATX090705' },
  { id: 4, text: 'WRWK300105' },
  { id: 5, text: 'GACX310935' },
]

type CustomItemTemplateContext = Omit<ItemTemplateContext, 'item'> & {
  item: CustomItem
}

const renderCustomItem = (context: CustomItemTemplateContext) => {
  return (
    <SearchBarItem ariaLabel={context.item.text}>
      <span>context.item.text</span>
    </SearchBarItem>
  )
}

const SearchBarStory = (args: any) => {
  return <SearchBar {...args} key={JSON.stringify(args)} />
}

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta: Meta<typeof SearchBar> = {
  title: 'Components/SearchBar',
  component: SearchBarStory,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    variant: {
      control: 'radio',
      options: ['md', 'lg'],
    },
    icon: {
      control: 'select',
      options: Object.keys(Icon),
      table: { type: { summary: 'string' } },
    },
  },
  decorators: [
    (Story) => {
      return <WijmoLicenseProvider license='""'>{Story()}</WijmoLicenseProvider>
    },
  ],
}

export default meta
type Story = StoryObj<typeof meta>

const getCustomItemsAsync = (query: string): Promise<any[] | null> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const res = customItems.filter((item) =>
        item.text.toLowerCase().startsWith(query.toLowerCase()),
      )

      resolve(res)
    }, 1000)
  })
}

export const Default: Story = {
  parameters: {
    docs: {
      description: {
        story: 'SearchBar',
      },
    },
  },
  args: {
    id: 'search-bar',
    srLabel: 'Search Bar',
    itemTemplate: renderCustomItem,
    placeholder: 'Type to Search',
    items: customItems,
    variant: 'md',
    icon: 'Search',
  },
}

export const AsyncGetItems: Story = {
  parameters: {
    docs: {
      description: {
        story: `SearchBar: controls have been disabled due to limitations on Wijmo's implementation of life-cycle events preventing hot-reload.
        \nSee "Default" version to test controls and property updates.`,
      },
    },
    layout: 'centered',
    controls: {
      exclude: [
        'id',
        'itemTemplate',
        'placeholder',
        'getItems',
        'variant',
        'icon',
        'initialValue',
        'items',
        'onSelectionChange',
        'disabled',
      ],
    },
  },

  args: {
    id: 'search-bar',
    srLabel: 'Search Bar',
    itemTemplate: renderCustomItem,
    placeholder: 'Type to Search',
    getItems: getCustomItemsAsync,
    variant: 'md',
    icon: 'Search',
  },
}

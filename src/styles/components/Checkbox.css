.checkbox-choice {
  --gatx-checkbox-bg: var(--gatx-colors-light-2);
  --gatx-checkbox-border-color: var(--gatx-colors-dark-5);
  --gatx-checkbox-outline-color: var(--gatx-colors-primary-7);
  --gatx-checkbox-disabled-color: var(--gatx-colors-light-8);

  @media (prefers-color-scheme: dark) {
    --gatx-checkbox-bg: var(--gatx-colors-dark-6);
    --gatx-checkbox-border-color: var(--gatx-colors-dark-2);
    --gatx-checkbox-outline-color: var(--gatx-colors-secondary-5);
    --gatx-checkbox-disabled-color: var(--gatx-colors-dark-4);
  }

  display: flex;
  align-items: center;
  justify-content: center;

  width: 16px;
  min-width: 16px;
  height: 16px;
  margin-top: var(--gatx-spacing-xs);
  border: 1px solid var(--gatx-checkbox-border-color);
  border-radius: var(--gatx-border-radius);
  background: transparent;

  &:not(:has(input:disabled)) {
    cursor: pointer;

    &:active,
    &:has(input:active),
    &:has(input:checked) {
      border: none;
      background: var(--gatx-colors-primary-6);
      color: var(--gatx-colors-light-2);
    }
  }

  &:has(input:disabled) {
    border: 1px solid var(--gatx-checkbox-disabled-color);
    background: var(--gatx-checkbox-bg);

    &:active,
    &:has(input:active),
    &:has(input:checked) {
      border: none;
      background: var(--gatx-checkbox-disabled-color);
      color: var(--gatx-checkbox-bg);
    }
  }

  &:focus-within {
    outline: 4px solid
      color-mix(in srgb, var(--gatx-checkbox-outline-color), transparent 40%);
  }
}

.checkbox-label:not(:has(input:disabled)) {
  cursor: pointer;
}

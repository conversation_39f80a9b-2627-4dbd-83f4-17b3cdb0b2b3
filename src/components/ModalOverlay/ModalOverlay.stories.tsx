import { action } from '@storybook/addon-actions'
import { withActions } from '@storybook/addon-actions/decorator'
import type { Meta, StoryObj } from '@storybook/react'
import { FC } from 'react'
import { useArgs, useCallback, useEffect } from 'storybook/internal/preview-api'
import { FooterProps, ModalOverlay } from '.'
import { Button } from '../Button'

/**
 * Footer for a single OK button - which will likely be the most common use-case.
 */
type PropsOKFooter = FooterProps & {
  onOk: () => void
}

export const ModalOverlayOKFooter: FC<PropsOKFooter> = ({
  onHide,
  onOk,
  ...props
}: PropsOKFooter) => {
  const onOKClick = () => {
    onOk()
    onHide?.()
  }

  return (
    <div className="modal-footer" {...props}>
      <Button text="OK" onClick={onOKClick} />
    </div>
  )
}

const BASIC_CONTENT = <p>Here is the content!</p>

const BIG_CONTENT = (
  <>
    <p>Here is the content!</p>
    <p>Line 1</p>
    <p>Line 2</p>
    <p>Line 3</p>
    <p>Line 4</p>
    <p>Line 5</p>
    <p>Line 6</p>
    <p>Line 7</p>
    <p>Line 8</p>
    <p>Line 9</p>
    <p>Line 10</p>
    <p>Line 11</p>
    <p>Line 12</p>
    <p>Line 13</p>
    <p>Line 14</p>
    <p>Line 15</p>
    <p>Line 16</p>
    <p>Line 17</p>
    <p>Line 18</p>
    <p>Line 19</p>
    <p>Line 20</p>
  </>
)

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta: Meta<typeof ModalOverlay> = {
  title: 'Components/ModalOverlay',
  component: ModalOverlay,
  tags: ['!autodocs'],
  parameters: {
    layout: 'centered',
  },
  decorators: [
    withActions,
    (Story, { tags }) => {
      const [, updateArgs] = useArgs()

      const setModalShow = useCallback(
        (modalShow: boolean) => {
          updateArgs({ show: modalShow })
        },
        [updateArgs],
      )

      useEffect(() => {
        if (tags.find((s) => s.toLowerCase() === 'autodocs')) {
          // Don't auto-show dialog for docs page.
          setModalShow(false)
        } else {
          setModalShow(true)
        }
        updateArgs({
          onHide: () => setModalShow(false),
        })
      }, [setModalShow, tags, updateArgs])

      return (
        <>
          <Button onClick={() => setModalShow(true)} text="Open Modal" />
          <Story />
        </>
      )
    },
  ],
}

export default meta
type Story = StoryObj<typeof meta>

export const BasicSmall: Story = {
  name: 'Variant: Small',
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        story: 'Basic modal, variant small',
      },
    },
  },
  args: {
    size: 'small',
    title: 'Basic Small',
    children: BASIC_CONTENT,
    footer: <ModalOverlayOKFooter onOk={action('OK Clicked')} />,
  },
}

export const BasicMedium: Story = {
  name: 'Variant: Medium',
  parameters: {
    docs: {
      description: {
        story: 'Basic modal, variant medium',
      },
    },
  },
  args: {
    size: 'medium',
    title: 'Basic Medium',
    children: BASIC_CONTENT,
    footer: <ModalOverlayOKFooter onOk={action('OK Clicked')} />,
  },
}

export const BasicLg: Story = {
  name: 'Variant: Large',
  parameters: {
    docs: {
      description: {
        story: 'Basic modal, variant large',
      },
    },
  },
  args: {
    size: 'large',
    title: 'Basic Large',
    children: BASIC_CONTENT,
    footer: <ModalOverlayOKFooter onOk={action('OK Clicked')} />,
  },
}

export const BigContentSm: Story = {
  name: 'With Big Content',
  parameters: {
    docs: {
      description: {
        story: 'Modal with a lot of content.',
      },
    },
  },
  args: {
    title: 'Big-Content',
    children: BIG_CONTENT,
    footer: <ModalOverlayOKFooter onOk={action('OK Clicked')} />,
  },
}

export const FullScreen: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Modal in full screen.',
      },
    },
  },
  args: {
    title: 'Full Screen',
    size: 'fullscreen',
    footer: <ModalOverlayOKFooter onOk={action('OK Clicked')} />,
  },
}

/**
 * A footer with multiple buttons.
 */
type PropsMultiFooter = FooterProps & {
  onOk: () => void
  onCancel: () => void
}

const MultiFooter: FC<PropsMultiFooter> = ({
  onHide,
  onOk,
  onCancel,
}: PropsMultiFooter & FooterProps) => {
  const onClick = (clientCallback: () => void) => {
    // Hide the dialog.
    if (onHide) {
      onHide()
    }

    // Inform the caller.
    clientCallback()
  }

  return (
    <>
      <Button
        variant="outline"
        onClick={() => onClick(onCancel)}
        text="Cancel"
      />
      <Button variant="primary" onClick={() => onClick(onOk)} text="OK" />
    </>
  )
}

export const MultiButtonMed: Story = {
  name: 'Multiple Buttons',
  parameters: {
    docs: {
      description: {
        story: 'A modal with multiple buttons.',
      },
    },
  },
  args: {
    title: 'Multiple Buttons',
    children: BASIC_CONTENT,
    footer: (
      <MultiFooter
        onOk={action('OK Clicked')}
        onCancel={action('Cancel Clicked')}
      />
    ),
  },
}

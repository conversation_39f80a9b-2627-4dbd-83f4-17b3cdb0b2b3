'use client'

import { AutoComplete as WijmoAutoComplete } from '@mescius/wijmo.input'
import { AutoComplete, ItemTemplateRender } from '@mescius/wijmo.react.input'
import clsx from 'clsx'
import { HTMLProps, useCallback, useState } from 'react'
import { SearchBarIcon } from './SearchBarIcon'

type ItemsSourceProps = {
  itemsSourceFunction?: any
  itemsSource?: any
}
type InitialValueProps = {
  text?: string
}

type Props = HTMLProps<HTMLElement> & {
  /** Identifier for the component */
  id: string
  /** Label for screenreader */
  srLabel: string
  /** (Optional) Value to be displayed when the component first renders */
  initialValue?: string
  /** Text to be displayed when there is no selection or initial value */
  placeholder?: string
  /** (Optional) Static list of items to be used of available options for the dropdown and type-ahead */
  items?: any[]
  /** (Optional) Async function used to retrieve the list of available options for the dropdown and type-ahead */
  getItems?: (query: string) => Promise<any[] | null>
  /** React element that will be used to display each items of the available options */
  itemTemplate?: ItemTemplateRender
  /** Event handler when selection changes */
  onSelectionChange?: (selectedItem: any) => void
  /** Size variant */
  variant?: 'md' | 'lg'
  /** Component's disabled state */
  disabled?: boolean
  /** String that matches Icon Component's names from React bootstrap icons */
  icon: string
  /** Message to show in the dropdown list if its empty*/
  emptyMessage?: string
}

const SearchBar = ({
  id,
  srLabel,
  initialValue,
  placeholder,
  items,
  getItems,
  itemTemplate,
  onSelectionChange,
  variant,
  disabled,
  icon,
  emptyMessage,
  ...props
}: Props) => {
  const [loadingData, setLoadingData] = useState(false)

  const itemsSourceProps: ItemsSourceProps = {}
  const initialValueProps: InitialValueProps = {}

  if (initialValue) {
    initialValueProps.text = initialValue
  }

  const onSelectedItemChange = () => {
    setLoadingData(false)
  }

  const onDropDownChange = (sender: WijmoAutoComplete) => {
    if (sender.selectedItem && !sender.isDroppedDown) {
      onSelectionChange?.(sender.selectedItem)
    }

    if (sender.text && sender.text.length < 3) {
      sender.isDroppedDown = false
      setLoadingData(false)
    }
  }

  const onTextChange = (sender: WijmoAutoComplete) => {
    if (itemsSourceProps.itemsSourceFunction) {
      if (sender.text && sender.text.length >= 3) {
        setLoadingData(true)
      } else {
        setLoadingData(false)
      }
    }

    if (itemsSourceProps.itemsSource && sender.text && sender.text.length < 3) {
      sender.isDroppedDown = false
    }
  }

  const handleFocusLost = () => {
    setLoadingData(false)
  }

  const getItemsSourceFunction = useCallback(
    (query: string, _max: number, callback: (res: any[]) => void) => {
      if (!query) {
        callback([])
        return
      }
      getItems?.(query)
        .then((results) => {
          if (results) {
            if (!results.length && emptyMessage) {
              callback([null])
              return
            }

            callback(results)
          }
        })
        .catch(() => console.error('Error running query'))
    },
    [getItems, emptyMessage],
  )

  /** This is needed because the AutoComplete component behaves differently
   * if itemsSourceFunction is set, even if it's undefined
   **/
  if (getItems) {
    itemsSourceProps.itemsSourceFunction = getItemsSourceFunction
  } else {
    itemsSourceProps.itemsSource = items
  }

  const renderItem: ItemTemplateRender = (context) => {
    if (context.item === null) {
      return <div className="search-bar-empty-message">{emptyMessage}</div>
    }

    return itemTemplate?.(context) as unknown
  }

  const handleInitialized = (sender: WijmoAutoComplete) => {
    if (sender) {
      const inputElement = sender.hostElement.querySelector('input')
      if (inputElement) {
        inputElement.setAttribute('aria-autocomplete', 'list')
        inputElement.setAttribute('aria-expanded', 'false')
        inputElement.setAttribute('aria-controls', `${id}_dropdown`)
        inputElement.setAttribute('role', 'combobox')
      }
    }
  }

  return (
    <div className="search-bar flex items-center">
      <label htmlFor={id} className="sr-only">
        {srLabel}
      </label>
      <AutoComplete
        id={id}
        placeholder={placeholder}
        className={clsx(
          props.className,
          variant === 'md' ? 'h-[40px]' : 'h-[50px]',
          variant === 'md' ? 'text-base' : 'text-lg',
        )}
        selectedIndexChanged={onSelectedItemChange}
        isDroppedDownChanged={onDropDownChange}
        wjItemTemplate={renderItem}
        textChanged={onTextChange}
        lostFocus={handleFocusLost}
        isDisabled={disabled}
        displayMemberPath="text"
        {...itemsSourceProps}
        {...initialValueProps}
        beginsWithSearch={!!initialValue}
        isDroppedDown={!!initialValue && !loadingData}
        initialized={handleInitialized}
        maxDropDownHeight={undefined}
      />
      <SearchBarIcon
        icon={icon}
        isLoading={loadingData}
        isDisabled={disabled}
      />
    </div>
  )
}

export type { ItemTemplateContext } from '@mescius/wijmo.react.input'
export { SearchBarItem } from './SearchBarItem'
export { SearchBar }

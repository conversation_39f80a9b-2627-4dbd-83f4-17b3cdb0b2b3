import { render, RenderResult, screen } from '@/tests/react'
import userEvent from '@testing-library/user-event'
import { axe } from 'jest-axe'
import { Tabs } from '.'

describe('Tabs', () => {
  let component: RenderResult

  beforeEach(() => {
    component = render(
      <Tabs.Container id="testing-tabs">
        <Tabs.Pane title="First Tab">
          <p>This is the first tab&apos;s content.</p>
        </Tabs.Pane>
        <Tabs.Pane title="Second Tab">
          <p>This is the second tab&apos;s content.</p>
        </Tabs.Pane>
      </Tabs.Container>,
    )
  })

  test('activates the first tab by default', () => {
    expect(screen.getByText("This is the first tab's content.")).toBeVisible()

    expect(
      screen.getByText("This is the second tab's content."),
    ).not.toBeVisible()
  })

  test('toggles between open tabs on click', async () => {
    const button = screen.getByRole('tab', { name: 'Second Tab' })
    await userEvent.click(button)

    expect(
      screen.getByText("This is the first tab's content."),
    ).not.toBeVisible()
    expect(screen.getByText("This is the second tab's content.")).toBeVisible()
  })

  test('adheres to accessibility guidelines', async () => {
    const { container } = component

    expect(await axe(container)).toHaveNoViolations()
  })
})

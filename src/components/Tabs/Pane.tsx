import { createEventKey } from '@/utils/createEventKey'
import { PropsWithChildren } from 'react'
import { TabPane } from 'react-bootstrap'
import { Heading, Landmark } from '../Content'

export type Props = {
  title: string
  className?: string
}

export const Pane = ({
  title,
  className,
  children,
}: PropsWithChildren<Props>) => {
  return (
    <TabPane eventKey={createEventKey(title)} className={className}>
      <Landmark virtual>
        <Heading text={title} screenreaderOnly />
        {children}
      </Landmark>
    </TabPane>
  )
}

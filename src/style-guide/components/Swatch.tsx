import { Unstyled } from '@storybook/blocks'
import { addons } from '@storybook/preview-api'
import { useCallback, useEffect, useState } from 'react'
import { DARK_MODE_EVENT_NAME } from 'storybook-dark-mode'

type Props = {
  prefix?: string
  colors: string[]
}

type ColorSwatch = {
  name: string
  hex: string
  variable: string
}

function colorSwatches(prefix: string, colors: string[]): ColorSwatch[] {
  const root = getComputedStyle(document.body)

  return colors.map((name) => {
    const variable = `--gatx-${prefix}-${name}`
    const hex = root.getPropertyValue(variable)
    return { name, hex, variable: `var(${variable})` }
  })
}

const Swatch = ({ prefix = 'colors', colors }: Props) => {
  const channel = addons.getChannel()
  const getSwatches = useCallback(
    () => colorSwatches(prefix, colors),
    [prefix, colors],
  )

  const [swatches, setSwatches] = useState<ColorSwatch[]>(getSwatches())

  useEffect(() => {
    const updateSwatches = () => setSwatches(getSwatches())
    channel.on(DARK_MODE_EVENT_NAME, updateSwatches)
    return () => channel.off(DARK_MODE_EVENT_NAME, updateSwatches)
  }, [channel, getSwatches])

  return (
    <Unstyled>
      <div className="docs-story">
        <table className="docs-swatches">
          <thead>
            <tr>
              <th></th>
              <th>Name</th>
              <th>Hex Code</th>
              <th>Variable</th>
            </tr>
          </thead>
          <tbody>
            {swatches.map((color) => {
              return (
                <tr key={color.name}>
                  <td>
                    <svg className="docs-swatch" aria-label={color.hex}>
                      <rect fill={color.variable} width="100%" height="100%" />
                    </svg>
                  </td>
                  <td>
                    <strong>{color.name}</strong>
                  </td>
                  <td>
                    <code>{color.hex}</code>
                  </td>
                  <td>
                    <code>{color.variable}</code>
                  </td>
                </tr>
              )
            })}
          </tbody>
        </table>
      </div>
    </Unstyled>
  )
}

export { Swatch }

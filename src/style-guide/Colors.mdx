{/* organize-imports-ignore */}
import { Meta } from '@storybook/blocks'
import { Swatch } from './components/Swatch'

<Meta title="Style Guide/Colors" />

# Colors

## Primary

Primary colors will be used for calls to action, and other important page elements

<Swatch
  colors={[
    'primary-1',
    'primary-2',
    'primary-3',
    'primary-4',
    'primary-5',
    'primary-6',
    'primary-7',
    'primary-8',
    'primary-9',
    'primary-10',
  ]}
/>

## Secondary

Secondary colors will be used for accent purposes.

<Swatch
  colors={[
    'secondary-1',
    'secondary-2',
    'secondary-3',
    'secondary-4',
    'secondary-5',
    'secondary-6',
    'secondary-7',
    'secondary-8',
    'secondary-9',
    'secondary-10',
  ]}
/>

## Tertiary

Tertiary colors will be used sparingly.

<Swatch
  colors={[
    'tertiary-1',
    'tertiary-2',
    'tertiary-3',
    'tertiary-4',
    'tertiary-5',
    'tertiary-6',
    'tertiary-7',
    'tertiary-8',
    'tertiary-9',
    'tertiary-10',
  ]}
/>

## Light

Light colors are used for background elements in light mode, and foreground elements where contrast with darker colors is necessary.

<Swatch
  colors={[
    'light-1',
    'light-2',
    'light-3',
    'light-4',
    'light-5',
    'light-6',
    'light-7',
    'light-8',
    'light-9',
    'light-10',
  ]}
/>

## Dark

Dark colors are used for background elements in dark mode, and foreground elements where contrast with lighter colors is necessary.

<Swatch
  colors={[
    'dark-1',
    'dark-2',
    'dark-3',
    'dark-4',
    'dark-5',
    'dark-6',
    'dark-7',
    'dark-8',
    'dark-9',
    'dark-10',
  ]}
/>

## Error

<Swatch
  colors={[
    'error-1',
    'error-2',
    'error-3',
    'error-4',
    'error-5',
    'error-6',
    'error-7',
    'error-8',
    'error-9',
    'error-10',
  ]}
/>

## Warning

<Swatch
  colors={[
    'warning-1',
    'warning-2',
    'warning-3',
    'warning-4',
    'warning-5',
    'warning-6',
    'warning-7',
    'warning-8',
    'warning-9',
    'warning-10',
  ]}
/>

## Success

<Swatch
  colors={[
    'success-1',
    'success-2',
    'success-3',
    'success-4',
    'success-5',
    'success-6',
    'success-7',
    'success-8',
    'success-9',
    'success-10',
  ]}
/>

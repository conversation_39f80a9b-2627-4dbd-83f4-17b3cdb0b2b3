import { useState } from 'react'
import { ReadOnlyInput } from '../ReadOnlyInput'

type Props = {
  /** The value of the text input */
  value?: string
  /** The function that will be called when the value of the input changes */
  onChange: (value: string) => void
  /** If the input is read only, and should render the ReadOnlyInput */
  readOnly?: boolean
  /** The id of the element that describes the input for error and helper text support */
  'aria-describedby'?: string
  /** Additional accessibility, if the input is invalid */
  'aria-invalid'?: boolean
  /** The id of the component. This will come from the form field component. */
  id?: string
  /** The placeholder of the input */
  placeholder?: string
}
/**
 * The TextInput component is used to allow users to input a text value.
 * It should be used alongside the FormField component to provide a label and error messages.
 * If you use it without the FormField component, provide an ID and label or aria-label for accessibility.
 */
const TextInput = ({
  value = '',
  onChange,
  readOnly = false,
  'aria-describedby': ariaDescribedBy,
  'aria-invalid': ariaInvalid,
  id,
  placeholder,
}: Props) => {
  /**
   * This is a solution to the issue of the input value being updated while the user is still typing.
   * It was a problem because the input value was being updated on every keystroke, which caused the cursor to jump to the end of the input,
   * not handling properly the combination of some characters (e.g. "a" + "¨" = "ä")
   */
  const [isComposing, setIsComposing] = useState(false)
  const [composingValue, setComposingValue] = useState(value)
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    if (isComposing) {
      setComposingValue(newValue)
    } else {
      onChange(newValue)
    }
  }

  return readOnly ? (
    <ReadOnlyInput value={value} id={id} />
  ) : (
    <input
      id={id}
      aria-describedby={ariaDescribedBy}
      aria-invalid={ariaInvalid}
      className="text-input"
      type="text"
      value={isComposing ? composingValue : value}
      onChange={handleChange}
      onCompositionStart={() => {
        setIsComposing(true)
        setComposingValue(value)
      }}
      onCompositionEnd={(e: React.CompositionEvent<HTMLInputElement>) => {
        setIsComposing(false)
        onChange((e.target as HTMLInputElement).value)
      }}
      autoComplete="off"
      placeholder={placeholder}
    />
  )
}

export { TextInput }

import { ElementType } from 'react'
import { CardChecklist, Map, Union } from 'react-bootstrap-icons'

export type ApplicationData = {
  icon: ElementType
  iconBackground: string
}

const APPLICATION_DATA: Record<string, ApplicationData> = {
  PLATFORM_ONE_SHOP_PORTAL: {
    icon: CardChecklist,
    iconBackground: '#0E619C',
  },
  PLATFORM_ONE_DIGITAL_TWIN_YARD: {
    icon: Map,
    iconBackground: '#BC5D1B',
  },
}
const DEFAULT_DATA: ApplicationData = {
  icon: Union,
  iconBackground: '#E27D36',
}

export const applicationData = (applicationCode: string) => {
  return APPLICATION_DATA[applicationCode] ?? DEFAULT_DATA
}

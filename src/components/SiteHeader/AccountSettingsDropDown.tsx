import { useUser } from '@/auth/useUser'
import { DropDown } from '@/components/DropDown'
import { useI18n } from '@/i18n/useI18n'
import clsx from 'clsx'
import { signOut } from 'next-auth/react'
import { forwardRef, useState } from 'react'
import { ButtonProps } from 'react-bootstrap'
import { ColorMode } from './ColorMode'
import { LogoutConfirmationModal } from './LogoutConfirmationModal'

const Toggle = forwardRef<
  HTMLButtonElement,
  Omit<ButtonProps, 'as'> & { name: string }
>(function Toggle({ name, ...props }, ref) {
  return (
    <button
      ref={ref}
      {...props}
      className={clsx(
        'inline-block rounded-[50%] h-[38px] w-[38px]',
        'bg-primary-7 dark:bg-primary-2 text-center leading-[38px] font-[600] text-light-6 dark:text-dark-8 uppercase',
        'hover:bg-primary-8 dark:hover:bg-primary-3',
        'focus-visible:outline-[4px] focus-visible:outline-solid',
        'transition-colors duration-150 motion-reduce:transition-none',
      )}
    >
      {name
        .split(' ')
        .map((n) => n.charAt(0))
        .join('')}
    </button>
  )
})

const AccountSettingsDropDown = () => {
  const { t } = useI18n('SiteHeader')
  const { name } = useUser()
  const [logoutModalVisible, setLogoutModalVisible] = useState(false)

  return (
    <div>
      <LogoutConfirmationModal
        show={logoutModalVisible}
        onCancel={() => setLogoutModalVisible(false)}
        onLogout={() => {
          signOut().catch((e) => console.error(e))
        }}
      />
      <DropDown.ToggleMenu
        label={t('accountSettings')}
        labelScreenreaderOnly
        Toggle={(props) => <Toggle {...props} name={name} />}
        noChevron
        dropAlign="end"
      >
        <div role="menuitem" className="p-md">
          <ColorMode />
        </div>
        <hr className="border border-light-7 dark:border-light-8" />
        <DropDown.Item
          label="Logout"
          icon="BoxArrowLeft"
          onClick={() => {
            setLogoutModalVisible(true)
          }}
        />
      </DropDown.ToggleMenu>
    </div>
  )
}

export { AccountSettingsDropDown }

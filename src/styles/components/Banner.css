.banner {
  --gatx-banner-bg: unset;
  --gatx-banner-color: unset;
  --gatx-banner-title-color: unset;
  --gatx-banner-btn-hover-bg: unset;
  --gatx-banner-btn-focus-bg: unset;
  --gatx-banner-btn-focus-outline: unset;

  display: inline-flex;
  align-items: center;

  width: 100%;
  padding: 8px 16px;
  background-color: var(--gatx-banner-bg);
  color: var(--gatx-banner-color);

  font-weight: var(--gatx-typography-font-weight-regular);
  font-size: var(--gatx-typography-font-size-regular);
  line-height: var(--gatx-typography-line-height);
  font-family: var(--gatx-typography-font-family);

  > .icon {
    width: var(--gatx-typography-font-size-regular);
    margin-right: 8px;
    color: var(--gatx-banner-color);
  }

  h2 {
    color: var(--gatx-banner-title-color);

    &::after {
      padding: 0 0.2rem;
      content: '-';
    }
  }

  button {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    padding: 0px 8px;
    border-radius: 3.2px;
    color: var(--gatx-banner-color);
    transition:
      background-color 0.15s ease-in-out,
      box-shadow 0.15s ease-in-out;

    @media (prefers-reduced-motion: reduce) {
      transition: none;
    }

    &:hover {
      background-color: color-mix(
        in srgb,
        var(--gatx-banner-btn-hover-bg),
        transparent 70%
      );
      color: var(--gatx-banner-color);
    }

    &:focus {
      outline: none;
      background-color: color-mix(
        in srgb,
        var(--gatx-banner-btn-hover-bg),
        transparent 70%
      );
      box-shadow: 0 0 0 4px var(--gatx-banner-btn-focus-outline);
    }
  }
}

.banner-warning {
  --gatx-banner-bg: var(--gatx-colors-warning-4);
  --gatx-banner-color: var(--gatx-colors-warning-8);
  --gatx-banner-header-color: var(--gatx-colors-warning-9);

  @media (prefers-color-scheme: dark) {
    --gatx-banner-bg: var(--gatx-colors-warning-3);
  }
}

.banner-success {
  --gatx-banner-bg: var(--gatx-colors-tertiary-1);
  --gatx-banner-color: var(--gatx-colors-tertiary-9);
  --gatx-banner-header-color: var(--gatx-colors-tertiary-10);

  --gatx-banner-btn-hover-bg: var(--gatx-colors-success-8);
  --gatx-banner-btn-focus-bg: var(--gatx-colors-success-8);
  --gatx-banner-btn-focus-outline: color-mix(
    in srgb,
    var(--gatx-colors-primary-7),
    transparent 60%
  );
}

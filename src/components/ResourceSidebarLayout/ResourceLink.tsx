import clsx from 'clsx'
import { ElementType } from 'react'

type Props = {
  title: string
  href: string
  Icon?: ElementType
  newTab?: boolean
  className?: string
}

const ResourceLink = ({
  title,
  href,
  Icon,
  newTab = false,
  className,
}: Props) => {
  const formattedAriaLabel = newTab ? `${title} (opens in a new tab)` : title

  return (
    <a
      href={href}
      target={newTab ? '_blank' : '_self'}
      rel={newTab ? 'noreferrer' : undefined}
      aria-label={formattedAriaLabel}
      className={clsx('resource-sidebar-item flex items-center', className)}
    >
      {Icon ? (
        <Icon className="resource-sidebar-icon size-md shrink-0" aria-hidden />
      ) : (
        <div className="size-md shrink-0" />
      )}

      <span className="ml-[24px] w-full">{title}</span>
    </a>
  )
}

export { ResourceLink }

import { type DefaultSession } from 'next-auth'
import { Authorization } from './types/Authorization'
import { Resource } from './types/Resource'
import { User } from './types/User'

declare module 'next-auth' {
  /**
   * Returned by `auth`, `useSession`, `getSession` and received as a prop on
   * the `SessionProvider` React Context
   */
  // eslint-disable-next-line @typescript-eslint/consistent-type-definitions
  interface Session {
    user: User & DefaultSession['user']
  }

  // eslint-disable-next-line @typescript-eslint/consistent-type-definitions
  interface JWT {
    id: string
    external: boolean
    authorizations: Authorization
    resources: Resource[]
  }
}

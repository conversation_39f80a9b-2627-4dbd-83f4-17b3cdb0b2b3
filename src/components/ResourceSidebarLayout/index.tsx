import { applicationData } from '@/auth/applicationData'
import { Application } from '@/auth/types/Authorization'
import { Resource } from '@/auth/types/Resource'
import { PropsWithChildren, useRef, useState } from 'react'
import { HouseDoorFill, Link45deg } from 'react-bootstrap-icons'
import { ResourceLink } from './ResourceLink'
import { ResourceSection } from './ResourceSection'
import { ResourceSidebar } from './ResourceSidebar'

type Props = PropsWithChildren & {
  homeApplication?: Application
  applications: Application[]
  resources: Resource[]
}

type ResourceSectionTitle = 'Links'

const ResourceSidebarLayout = ({
  homeApplication,
  applications,
  resources,
  children,
}: Props) => {
  const [isOpen, setIsOpen] = useState(false)
  const [openSections, setOpenSections] = useState<ResourceSectionTitle[]>([])

  const queued = useRef({
    sections: [] as ResourceSectionTitle[],
    closing: false,
  })

  const handleToggleSideBar = () => {
    if (isOpen && openSections.length) {
      queued.current.closing = true
      setOpenSections([])
      return
    }

    if (isOpen) {
      setIsOpen(false)
    } else {
      setIsOpen(true)
    }
  }

  const handleToggleSection = (section: ResourceSectionTitle) => {
    if (!isOpen) {
      setIsOpen(true)
      queued.current.sections.push(section)
      return
    }

    if (openSections.includes(section)) {
      setOpenSections((state) => state.filter((s) => s !== section))
    } else {
      setOpenSections((state) => [...state, section])
    }
  }

  const handleToggleEnd = () => {
    if (queued.current.sections.length > 0) {
      setOpenSections(queued.current.sections)
      queued.current.sections = []
    }

    if (queued.current.closing) {
      queued.current.closing = false
      setIsOpen(false)
    }
  }

  return (
    <div className="flex h-screen overflow-clip w-full">
      <ResourceSidebar
        isOpen={isOpen}
        onToggle={handleToggleSideBar}
        onToggleEnd={handleToggleEnd}
      >
        <div className="resource-sidebar-content">
          <ul className="resource-sidebar-section">
            <li>
              <ResourceLink
                title={homeApplication?.name ?? 'Platform One'}
                href={homeApplication?.uri ?? '/'}
                Icon={HouseDoorFill}
                className="resource-sidebar-header-title"
              />
            </li>
          </ul>
          <ul className="resource-sidebar-section">
            {applications.map((application) => (
              <li key={application.code}>
                <ResourceLink
                  title={application.name}
                  href={application.uri ?? ''}
                  Icon={applicationData(application.code).icon}
                />
              </li>
            ))}
          </ul>
          <ul className="resource-sidebar-section">
            <li>
              <ResourceSection
                title="Links"
                Icon={Link45deg}
                isOpen={openSections.includes('Links')}
                onToggle={() => handleToggleSection('Links')}
                onToggleEnd={handleToggleEnd}
              >
                {resources.map((resource) => (
                  <li key={resource.title}>
                    <ResourceLink
                      href={resource.href}
                      title={resource.title}
                      className="underline!"
                      newTab
                    />
                  </li>
                ))}
              </ResourceSection>
            </li>
          </ul>
        </div>
      </ResourceSidebar>
      <div className="min-h-0 h-screen overflow-y-auto flex-1">{children}</div>
    </div>
  )
}

export { ResourceSidebarLayout }

# PlatformOne Common

This package contain common artifacts to be used in PlatformOne's client apps. It holds the Component Library defined by the official Design System and other utilities to be imported in React or NextJS apps.

[Platform One Component Library](./src/components/README.md)!

## Development

This package uses `bun` as package manager, `vite` as build tool and `jest` as test runner. Beware of the correct commands, so the scripts from `package.json` are referenced instead of built-in `bun` programs:

|              | ✅              | ❌          |
| ------------ | --------------- | ----------- |
| Building     | `bun run build` | `bun build` |
| Running test | `bun run test`  | `bun test`  |

### Installation & Running Storybook

The development environment is a [Storybook](https://storybook.js.org/docs) containing the style guide and reusable components, mostly from the Design System.

1. On initial set up with `bun install` to install dependencies.
2. Run `bun run build` to build project.
3. To run local Storybook, `bun dev`

### Developing with Other Applications

When developing other utilities, tools like [`yarn link`](https://classic.yarnpkg.com/lang/en/docs/cli/link/) or [`yalc`](https://github.com/wclr/yalc) might be used to access local versions of the package from other applications. Alternatively, the package can be installed directly from GitHub and built locally inside `node_modules/@gatx/platform-one-common`.

## Creating a PR

PRs **must** be tagged, as semantic tags result in a version bump. The version-changing tags are:

| Tag               | Digit | Example            |
| ----------------- | ----- | ------------------ |
| `BREAKING CHANGE` | Major | `1.0.0` -> `2.0.0` |
| `feat`            | Minor | `1.0.0` -> `1.1.0` |
| `fix`             | Patch | `1.0.0` -> `1.0.1` |

`BREAKING CHANGE` PRs **must** include `BREAKING CHANGE: [description]` in the PR description or commit footer. The PR title should include a tag that closest aligns with the changes.

Other non-version-changing tags to use, such as `chore`, `docs`, `refactor` and `test`.

It is recommended to scope your PR `feat(scope)` (i.e. `feat(toast)` or `feat(ticket_number)`) as this improves Release Notes that are created after the Release Workflow completes.

### How to decide on a tag

There are fairly comprehensive guidelines given in the [Semantic Versioning 2.0.0 spec](https://semver.org/). Here are some helpful guidelines to follow if you don't have time to read through all of that, though:

- `BREAKING CHANGE`: This should be used any time you change the expected behavior or interface for an existing element in the codebase. Changing a property name or type, or adding a new required property to an existing component would be common examples of this. If somebody is already consuming the package, this tag indicates that they might need to change how they're using the package in response to the update.
- `feat`: This should be used any time you're adding something backwards-compatible to the codebase. A new (optional) property for an existing component, an entirely new component, or added styles to support additional use cases would be examples of this. If somebody is already consuming the package, this tag indicates that updating the package will expand the package's API without requiring any code changes to the application.
- `fix`: This should be used any time you're correcting the behavior of existing code in a way that doesn't result in a breaking change. Updating the use of an existing prop for a component that wasn't being properly respected would be an example. If somebody is already consuming the package, this tag indicates that updating the package will fix underlying issues without introducing any API changes or breaking any (specified) behavior.

## Creating a Release

Releases must be triggered manually.

1. Navigate to the `Actions` tab
2. Navigate to the `Test, Build & Release` workflow
3. A button to trigger the release process will be available in that part of GitHub's user interface. Releases must be done on `main`

When the workflow is done, a version number will be provided. To update the package version in another repo run `yarn up @gatx-corp/platform-one-common` to bump the version. Then commit it and open a PR.

## Technical Details

### Using the Package

This is a private package hosted in GitHub Packages, so consumers must point their package manage to the NPM Registry managed by GitHub, and provide a Personal Access Token (PAT) with `read:packages` permissions.

A `.npmrc` can be used to point to the correct registry:

```
@gatx-corp:registry=https://npm.pkg.github.com/
//npm.pkg.github.com/:_authToken=${GITHUB_TOKEN}
```

Alternatively, `.yarnc.yml` can be used if it already exists in the app:

```
npmScopes:
  gatx-corp:
    npmRegistryServer: 'https://npm.pkg.github.com'
    npmAlwaysAuth: true
    npmAuthToken: '${GITHUB_TOKEN}'
```

These examples read the GitHub PAT from a `GITHUB_TOKEN` environment variable, though this can be decided by the consumer. The PAT can be generated from GitHub's UI or retrieved using GitHub's CLI. For example: `export GITHUB_TOKEN = $(gh auth token)`. See [related docs](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens).

Once the registry is properly configured and the `GITHUB_TOKEN` variable is set with a PAT with `read:packages` scope, the last version of the package can be installed via:

```shell
yarn install @gatx-corp/platform-one-common
```

### Importing Styles

This package relies on Tailwind and PostCSS, which can be installed with the following command:

```
yarn install tailwindcss @tailwindcss/postcss postcss
```

Postcss must be configured with at least the following plugins:

```js
// postcss.config.js

module.exports = {
  plugins: {
    '@tailwindcss/postcss': {},
    '@gatx-corp/platform-one-common/postcss': {},
  },
}
```

The base styles and the tailwind configuration must be imported, either in a css file that's referenced by the application, or directly in a Typescript/Javascript file:

```
import "@gatx-corp/platform-one-common/styles"
import "@gatx-corp/platform-one-common/tailwind"
```

### Building

The package is built using `vite`, into the `BUILD_DIR` specified in `vite.config.ts`, using the `bun run build` command. Instead of a single barrel file with all the exported artifacts, there are many exposed entrypoints which make up the public API of the package:

- `src/components/**/index.ts` (Components)
- `src/components/**/index.tsx` (Components)
- `src/auth/index.ts` (Client-Side Auth Utilities)
- `src/auth/next-auth.ts` (Server-Side Auth Utilities)
- `src/icons/*.tsx` (Icons)
- `src/stores/**/index.ts` (Global State Stores)
- `src/stores/**/index.tsx`(Global State Stores)
- `src/index.tsx` (General Exports)
- `src/wijmo.tsx` (Wijmo License Provider)

Every element exported from files matching the patterns above will be accessible from consumer applications. Complementarily, other elements will be package-private. The `exports` key in the `package.json` specify how each of these entrypoints will be accessed from consumers applications, while the `vite.config.ts` states the algorithm to bundle these files.

### Releasing

The release process is fairly automated by the [`semantic-release`](https://github.com/semantic-release/semantic-release), which will analyze commits from the most recent version tag to determine the new version and publish it to GitHub Packages. Commit messages must have semantic tags to result in version bump, for example: `git commit -m "feat(button): add hover styles"`.

Tagged commits **MUST** reach the `main` branch and, ideally, a single PR will not bump the version more than once. Thus, the recommended way of merging version-changing changes to `main` is via Squash & Merge. Developers using merge commits must be mindful about adding a single tagged commit to their PR. The `semantic-pr.yml` workflow controls that the PR's title has an appropriate tag, as a reminder. Developers using Squash & Merge will benefit from this, as the PR's title is the default commit message in this case.

import { useI18n } from '@/i18n/useI18n'
import clsx from 'clsx'
import { HTMLAttributes, PropsWithChildren } from 'react'

type Props = HTMLAttributes<HTMLAnchorElement> & {
  /** The style variant for the component. */
  variant: 'underline' | 'outline'
  /** URL that the link points to. */
  href: string
  /** Optional, whether the link will open in a new tab. */
  newTab?: boolean
  /** Accessible label for the link. */
  'aria-label': React.ComponentProps<'a'>['aria-label']
}

/**
 * The Link component is used to navigate to a different page or resource.
 */
const Link = ({
  variant,
  href,
  newTab = false,
  'aria-label': ariaLabel,
  children,
  className,
  ...props
}: PropsWithChildren<Props>) => {
  const { t } = useI18n('Link')
  const formattedAriaLabel = newTab
    ? t('newTabAriaLabel', { ariaLabel: ariaLabel ?? '' })
    : aria<PERSON>abel

  const { target, rel } = newTab
    ? { target: '_blank', rel: 'noreferrer' }
    : { target: '_self', rel: undefined }

  return (
    <a
      className={clsx('link', `link-${variant}`, className)}
      href={href}
      target={target}
      rel={rel}
      aria-label={formattedAriaLabel}
      {...props}
    >
      {children}
    </a>
  )
}

export { Link }

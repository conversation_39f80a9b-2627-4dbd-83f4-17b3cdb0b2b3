import type { Meta, StoryObj } from '@storybook/react'
import { useState } from 'react'
import { Accordion } from '.'
import { Badge } from '../Badge'
import { ChoiceGroup } from '../ChoiceGroup'
import { Table } from '../Table'

const meta: Meta<typeof Accordion.Container> = {
  title: 'Components/Accordion',
  component: Accordion.Container,
  parameters: {
    layout: 'padded',
    actions: { handles: ['click'] },
  },
  subcomponents: { Item: Accordion.Item as React.ComponentType<unknown> },
}

export default meta
type Story = StoryObj<typeof meta>

export const Card: Story = {
  name: 'Variant: Card',
  parameters: {
    docs: {
      description: {
        story: `Card accordions are used to represent a set of top-level
          sections or records, where the accordions serve as the primary
          organizational element for the content in the view.`,
      },
    },
  },
  args: {
    variant: 'card',
    children: [
      <Accordion.Item title="Accordion Number One" key="test-1">
        <p>This is some content inside the first accordion.</p>
      </Accordion.Item>,
      <Accordion.Item title="Accordion Number Two" key="test-2">
        <p>And here is some content that is inside the second accordion.</p>
      </Accordion.Item>,
    ],
  },
}

export const Flush: Story = {
  name: 'Variant: Flush',
  parameters: {
    docs: {
      description: {
        story: `Flush accordions are used to represent a set of subordinate
          sections or records, with consecutive accordion items containing
          related content or content describing the same parent element.`,
      },
    },
  },
  args: {
    variant: 'flush',
    children: [
      <Accordion.Item title="Accordion Number One" key="test-1">
        <p>This is some content inside the first accordion.</p>
      </Accordion.Item>,
      <Accordion.Item title="Accordion Number Two" key="test-2">
        <p>And here is some content that is inside the second accordion.</p>
      </Accordion.Item>,
    ],
  },
}

export const WithDisabledItems: Story = {
  name: 'Example: With Disabled Items',
  parameters: {
    docs: {
      description: {
        story: `Accordion items can be disabled to prevent interaction.`,
      },
    },
  },

  render: () => {
    return (
      <div className="flex flex-col gap-lg">
        {(['card', 'flush'] as const).map((variant) => (
          <Accordion.Container variant={variant} key={variant}>
            <Accordion.Item
              title={`A ${variant} accordion`}
              key="test-1"
              disabled
            >
              <p>This is some content inside the accordion item.</p>
            </Accordion.Item>
          </Accordion.Container>
        ))}
      </div>
    )
  },
}

export const WithIndicators: Story = {
  name: 'Example: With Indicators',
  parameters: {
    docs: {
      description: {
        story: `Accordion items accept indicators, used to bring attention to the item when it's closed. `,
      },
    },
  },

  render: () => {
    return (
      <div className="flex flex-col gap-lg">
        {(['card', 'flush'] as const).map((variant) => (
          <Accordion.Container variant={variant} key={variant}>
            <Accordion.Item
              title={`A ${variant} accordion`}
              key="test-1"
              indicators={
                <>
                  <Badge label="Badge1" />
                  <Badge label="Badge2" />
                </>
              }
            >
              <p>This is some content inside the accordion item.</p>
            </Accordion.Item>
          </Accordion.Container>
        ))}
      </div>
    )
  },
}

export const WithTable: Story = {
  name: 'Example: With Table',
  parameters: {
    docs: {
      description: {
        story: `Accordion items accept indicators, used to bring attention to the item when it's closed. `,
      },
    },
  },

  render: () => {
    return (
      <div className="flex flex-col gap-lg">
        {(['card', 'flush'] as const).map((variant) => (
          <Accordion.Container variant={variant} key={variant}>
            <Accordion.Item
              title={`A ${variant} accordion`}
              key="test-1"
              indicators={
                <>
                  <Badge label="Badge1" />
                  <Badge label="Badge2" />
                </>
              }
            >
              <Table
                columnHeaders={['Program', 'Program Description', 'Due']}
                rows={[
                  ['EN-667', 'INSPECT JBURY AZFRC INSERT', '22 APR 2014'],
                  ['EN-690', 'DO NOT OVERPAINT RAILCARS', '30 OCT 2015'],
                  ['IO-533-1', 'PETRO CANADA LUBRICANTS', '22 JAN 2017'],
                ]}
              />
            </Accordion.Item>
          </Accordion.Container>
        ))}
      </div>
    )
  },
}

export const AutoCloseDisabledItems: Story = {
  name: 'Auto-Close Disabled Items',
  parameters: {
    docs: {
      description: {
        story: `Accordions items will automatically close when they are disabled.`,
      },
    },
  },

  render: () => {
    const [disabled, setDisabled] = useState<string[]>([])

    return (
      <div className="flex flex-col gap-lg">
        <div className="bg-light-1 dark:bg-dark-2 flex gap-sm p-sm rounded">
          <ChoiceGroup
            label="Disabled Items"
            options={[
              { label: 'First Item', value: 'first' },
              { label: 'Second Item', value: 'second' },
            ]}
            value={disabled}
            onChange={setDisabled}
            multiSelect
          />
        </div>
        <Accordion.Container variant="card" key="test-1" hasExpandCollapse>
          <Accordion.Item
            title="First Item"
            key="test-1"
            disabled={disabled.includes('first')}
          >
            <p>This is some content inside the accordion item.</p>
          </Accordion.Item>
          <Accordion.Item
            title="Second Item"
            key="test-2"
            disabled={disabled.includes('second')}
          >
            <p>This is some content inside the accordion item.</p>
          </Accordion.Item>
        </Accordion.Container>
      </div>
    )
  },
}

export const WithExpandAllCloseAll: Story = {
  name: 'Example: With Expand & Close All',
  parameters: {
    docs: {
      description: {
        story: `Card style accordion with "Expand All" and "Close All" functionality`,
      },
    },
  },

  render: () => {
    return (
      <div className="flex flex-col gap-lg">
        <Accordion.Container
          variant={'card'}
          hasExpandCollapse={true}
          screenReaderContext="Main Accordions"
        >
          <Accordion.Item title="Accordion Number One" key="test-1">
            <p>This is some content inside the first accordion.</p>
          </Accordion.Item>
          <Accordion.Item title="Accordion Number Two" key="test-2">
            <p>And here is some content that is inside the second accordion.</p>
          </Accordion.Item>
        </Accordion.Container>
      </div>
    )
  },
}

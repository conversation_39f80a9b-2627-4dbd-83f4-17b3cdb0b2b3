.drawing-link-card {
  --gatx-drawing-link-card-bg: var(--gatx-colors-secondary-2);
  --gatx-drawing-link-card-hover-bg: var(--gatx-colors-secondary-3);
  --gatx-drawing-link-card-color: var(--gatx-colors-dark-8);
  --gatx-drawing-link-card-latest: var(--gatx-colors-secondary-6);
  --gatx-drawing-link-card-disabled-bg: var(--gatx-colors-light-7);
  --gatx-drawing-link-card-disabled-color: var(--gatx-colors-light-9);
  --gatx-drawing-link-card-focus-color: var(--gatx-colors-primary-4);
  --gatx-drawing-link-card-outline-color: var(--gatx-colors-primary-7);
  --gatx-drawing-link-card-outline-transparency: 60%;

  @media (prefers-color-scheme: dark) {
    --gatx-drawing-link-card-bg: var(--gatx-colors-primary-2);
    --gatx-drawing-link-card-hover-bg: var(--gatx-colors-primary-3);
    --gatx-drawing-link-card-latest: var(--gatx-colors-primary-5);
    --gatx-drawing-link-card-disabled-bg: var(--gatx-colors-dark-4);
    --gatx-drawing-link-card-disabled-color: var(--gatx-colors-dark-8);
    --gatx-drawing-link-card-focus-color: var(--gatx-colors-secondary-3);
    --gatx-drawing-link-card-outline-color: var(--gatx-colors-secondary-5);
    --gatx-drawing-link-card-outline-transparency: 40%;
  }

  position: relative;
  min-width: 150px;
  padding: 10px;

  border-radius: var(--gatx-border-radius);
  background: var(--gatx-drawing-link-card-bg);
  color: var(--gatx-drawing-link-card-color);
  font-size: var(--gatx-typography-font-size-sm);
  text-align: start;

  &:hover,
  &:active {
    background: var(--gatx-drawing-link-card-hover-bg);
  }

  &:disabled {
    --gatx-drawing-link-card-latest: var(
      --gatx-drawing-link-card-disabled-color
    );
    background: var(--gatx-drawing-link-card-disabled-bg);
    color: var(--gatx-drawing-link-card-disabled-color);
  }

  &:focus,
  &:focus-within,
  &:focus-visible {
    border-radius: var(--gatx-border-radius);
    outline: 4px solid
      color-mix(
        in srgb,
        var(--gatx-drawing-link-card-outline-color),
        transparent var(--gatx-drawing-link-card-outline-transparency)
      );
  }

  .latest {
    position: absolute;
    top: var(--gatx-spacing-sm);
    right: var(--gatx-spacing-xs);
    height: var(--gatx-spacing-sm);
    color: var(--gatx-drawing-link-card-latest);
  }
}

import { withActions } from '@storybook/addon-actions/decorator'
import type { Args, <PERSON>a, StoryObj } from '@storybook/react'
import { useState } from 'react'
import { ChoiceGroup } from '.'

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta: Meta = {
  title: 'Components/ChoiceGroup',
  component: ChoiceGroup,
  parameters: {
    layout: 'centered',
    actions: { handles: ['change'] },
  },
  argTypes: {
    multiSelect: {
      name: 'multiSelect (required)',
      description:
        'Whether the chip group allows for multiple values to be selected.',
      table: {
        type: {
          summary: 'boolean',
        },
      },
      control: {
        disable: true,
      },
    },
    onChange: {
      name: 'onChange (required)',
      description: 'The function called when the selected values change.',
      table: {
        type: {
          summary: '(value: T) => void',
        },
      },
      control: { disable: true },
    },
    value: {
      name: 'value (required)',
      description: 'The selected value(s) for the group of chips.',
      table: {
        type: {
          summary: 'T | T[]',
        },
      },
      control: { disable: true },
    },
  },
  decorators: [
    (Story) => {
      return (
        <div className="p-md rounded docs-foreground">
          <Story />
        </div>
      )
    },
    withActions,
  ],
}

export default meta
type Story = StoryObj<typeof meta>

export const SingleSelectChips: Story = {
  name: 'Single Select',
  render: (args: Args) => <RadioGroup {...args} />,
  args: {
    size: 'small',
    label: 'Example Label',
    options: [
      { label: 'Yes', value: 'y' },
      { label: 'No', value: 'n' },
    ],
  },
  parameters: {
    docs: {
      description: {
        story:
          'Single select chips should be used when the user is able to select only one option. For example, a yes/no question.',
      },
    },
  },
}

const RadioGroup = ({ ...args }) => {
  const [value, setValue] = useState<string>('y')

  return (
    <ChoiceGroup
      error={args.error as string}
      description={args.description as string}
      labelScreenreaderOnly={args.screenreaderOnly as boolean}
      disabled={args.disabled as boolean}
      label={args.label as string}
      size={args.size as 'small' | 'large'}
      multiSelect={false}
      options={args.options as { label: string; value: string }[]}
      value={value}
      onChange={setValue}
      readOnly={args.readOnly as boolean}
    />
  )
}

export const MultiSelectCheckRadioGroup: Story = {
  name: 'Multi Select',
  render: (args: Args) => <CheckboxGroup {...args} />,
  args: {
    size: 'small',
    label: 'Example Label',
    options: [
      { label: 'Alpha', value: 'a' },
      { label: 'Beta', value: 'b' },
      { label: 'Charlie', value: 'c' },
      { label: 'Delta', value: 'd' },
      { label: 'Echo', value: 'e' },
      { label: 'Foxtrot', value: 'f' },
    ],
  },
  parameters: {
    docs: {
      description: {
        story:
          'Multi select chips should be used when the user is able to select multiple options.',
      },
    },
  },
}

const CheckboxGroup = ({ ...args }) => {
  const [values, setValues] = useState<string[]>(['f'])

  return (
    <ChoiceGroup
      error={args.error as string}
      description={args.description as string}
      labelScreenreaderOnly={args.screenreaderOnly as boolean}
      disabled={args.disabled as boolean}
      label={args.label as string}
      size={args.size as 'small' | 'large'}
      multiSelect={true}
      options={args.options as { label: string; value: string }[]}
      value={values}
      onChange={setValues}
      readOnly={args.readOnly as boolean}
    />
  )
}

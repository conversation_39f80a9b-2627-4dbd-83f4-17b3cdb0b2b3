{/* organize-imports-ignore */}
import { Meta } from '@storybook/blocks'
import { Swatch } from './components/Swatch'

<Meta title="Style Guide/Colors/Variables" />

# Utility Variables

We use several color-scheme-aware variables to ensure proper contrast across
both light and dark modes. You can see how these values change when the color
scheme changes.

## Main

<Swatch prefix="color" colors={['bg', 'primary', 'primary-dark']} />

## Typography

<Swatch
  prefix="typography-color"
  colors={['text', 'text-subtle', 'link', 'link-hover']}
/>
